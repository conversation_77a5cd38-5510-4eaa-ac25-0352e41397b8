{"name": "local-storage", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "packages/local-storage/src", "prefix": "lib", "projectType": "library", "tags": [], "targets": {"test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "packages/local-storage/jest.config.ts"}}, "lint": {"executor": "@nx/eslint:lint"}}}