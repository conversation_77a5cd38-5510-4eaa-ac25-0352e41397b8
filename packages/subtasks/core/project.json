{"name": "subtasks-core", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "packages/subtasks/core/src", "prefix": "lib", "projectType": "library", "tags": [], "targets": {"test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "packages/subtasks/core/jest.config.ts"}}, "lint": {"executor": "@nx/eslint:lint"}}}