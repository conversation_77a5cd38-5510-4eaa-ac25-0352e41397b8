{"name": "subtasks-workflow-integration", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "packages/subtasks/workflow-integration/src", "prefix": "lib", "projectType": "library", "tags": [], "targets": {"test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "packages/subtasks/workflow-integration/jest.config.ts"}}, "lint": {"executor": "@nx/eslint:lint"}}}