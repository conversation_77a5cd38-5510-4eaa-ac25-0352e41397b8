{"name": "subtasks-escalations", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "packages/subtasks/escalations/src", "prefix": "lib", "projectType": "library", "tags": [], "targets": {"test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "packages/subtasks/escalations/jest.config.ts"}}, "lint": {"executor": "@nx/eslint:lint"}}}