{"name": "history-adapter-unit", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "packages/history-adapters/unit/src", "prefix": "lib", "projectType": "library", "tags": [], "targets": {"test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "packages/history-adapters/unit/jest.config.ts"}}, "lint": {"executor": "@nx/eslint:lint"}}}