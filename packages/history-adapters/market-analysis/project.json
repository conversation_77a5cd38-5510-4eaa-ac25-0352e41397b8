{"name": "history-adapter-market-analysis", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "packages/history-adapters/market-analysis/src", "prefix": "lib", "projectType": "library", "tags": [], "targets": {"test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "packages/history-adapters/market-analysis/jest.config.ts"}}, "lint": {"executor": "@nx/eslint:lint"}}}