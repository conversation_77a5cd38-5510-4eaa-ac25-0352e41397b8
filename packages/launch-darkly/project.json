{"name": "launch-darkly", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "packages/launch-darkly/src", "prefix": "lib", "projectType": "library", "tags": [], "targets": {"test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "packages/launch-darkly/jest.config.ts"}}, "lint": {"executor": "@nx/eslint:lint"}}}