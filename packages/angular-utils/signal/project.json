{"name": "angular-signal-utils", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "packages/angular-utils/signal/src", "prefix": "lib", "projectType": "library", "tags": [], "targets": {"test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "packages/angular-utils/signal/jest.config.ts"}}, "lint": {"executor": "@nx/eslint:lint"}}}