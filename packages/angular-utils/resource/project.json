{"name": "angular-resource-utils", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "packages/angular-utils/resource/src", "prefix": "lib", "projectType": "library", "tags": [], "targets": {"test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "packages/angular-utils/resource/jest.config.ts"}}, "lint": {"executor": "@nx/eslint:lint"}}}