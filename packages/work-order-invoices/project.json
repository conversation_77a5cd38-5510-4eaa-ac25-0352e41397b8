{"name": "work-order-invoices", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "packages/work-order-invoices/src", "prefix": "lib", "projectType": "library", "tags": [], "targets": {"test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "packages/work-order-invoices/jest.config.ts"}}, "lint": {"executor": "@nx/eslint:lint"}}}