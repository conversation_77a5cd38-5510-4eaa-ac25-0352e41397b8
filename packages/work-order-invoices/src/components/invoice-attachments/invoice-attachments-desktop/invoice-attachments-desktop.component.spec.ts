import { IMyndFileDto } from '@myndmanagement/api-files';
import {
  IMyndUpload,
  IMyndUploadCompleteResponse,
  MyndAttachmentsService,
  MyndFileUploadSelectorComponent,
} from '@myndmanagement/attachments';
import { MyndCommonModule } from '@myndmanagement/common';
import { MYND_API_ROOT_TOKEN } from '@myndmanagement/http';
import { runtimeChecks } from '@myndmanagement/test';
import { MyndButtonComponent, MyndLabelActionComponent, MyndLabelComponent } from '@myndmanagement/ui-forms';
import { MyndModalsModule } from '@myndmanagement/ui-modals';
import { MyndTableModule } from '@myndmanagement/ui-table';
import { myndProvideToastrServiceMock } from '@myndmanagement/ui-toast/testing';
import { Spectator, createComponentFactory, mockProvider, byTestId } from '@ngneat/spectator/jest';
import { EffectsModule } from '@ngrx/effects';
import { StoreModule } from '@ngrx/store';
import { FileUploadModule } from 'ng2-file-upload';
import { of, take } from 'rxjs';

import { MyndWorkOrderInvoiceAttachmentsDesktopComponent } from './invoice-attachments-desktop.component';

let file: IMyndFileDto;

describe('MyndWorkOrderInvoiceAttachmentsDesktopComponent', () => {
  let spectator: Spectator<MyndWorkOrderInvoiceAttachmentsDesktopComponent>;

  const createComponent = createComponentFactory<MyndWorkOrderInvoiceAttachmentsDesktopComponent>({
    component: MyndWorkOrderInvoiceAttachmentsDesktopComponent,
    imports: [
      FileUploadModule,
      MyndModalsModule,
      MyndTableModule,
      MyndCommonModule,
      EffectsModule.forRoot([]),
      StoreModule.forRoot({}, { runtimeChecks }),
      MyndLabelComponent,
      MyndLabelActionComponent,
    ],
    declarations: [
      MyndButtonComponent,
      MyndFileUploadSelectorComponent,
    ],
    providers: [
      { provide: MYND_API_ROOT_TOKEN, useValue: 'test-api-root' },
      mockProvider(MyndAttachmentsService, {
        getFile: jest.fn(() => of(file)),
      }),
      myndProvideToastrServiceMock(),
    ],
  });

  beforeEach(() => {
    spectator = createComponent();

    file = {
      fileId: 'some-file-id',
      fileName: 'some file name',
      fileUrl: 'http://some-file.jpg',
      contentType: 'some-content-type',
      contentLength: 123456,
    };
  });

  it('should start files apploading with correct url and show loader', () => {
    const fileUpload = {
      files: [
        { file: { name: 'photo1.jpg', url: 'http://mynd.co/photo1.jpg' } },
      ],
    } as unknown as IMyndUpload;

    expect(spectator.query('m-throbber')).not.toExist();

    spectator.setInput({ fileUploadingToken: 'some-token' });
    spectator.triggerEventHandler('m-file-upload-selector', 'getAttachments', fileUpload);

    expect(spectator.query('m-throbber')).toExist();
    expect(fileUpload.files[0].url).toBe(
      'test-api-root/files?token=some-token&file-name=photo1.jpg&file-type=WO_INVOICE',
    );
  });

  it('should show uploaded files, hide loader, emit files change event', () => {
    const fileUploadResponse = { response: JSON.stringify(file) } as unknown as IMyndUploadCompleteResponse;
    spectator.component.isUploading = true;
    spectator.detectComponentChanges();

    expect(spectator.query('m-throbber')).toExist();

    const filesChangeEmitSpy = jest.spyOn(spectator.component.filesChange, 'emit');
    spectator.triggerEventHandler('m-file-upload-selector', 'completeItem', fileUploadResponse);

    expect(spectator.query('m-throbber')).not.toExist();
    expect(spectator.queryAll('m-table-row')[1]).toContainText(file.fileName);
    expect(filesChangeEmitSpy).toHaveBeenCalledWith([file]);
  });

  it('should hide loader if uploaded file does not have fileId and not show such files', () => {
    delete file.fileId;
    const fileUploadResponse = { response: JSON.stringify(file) } as unknown as IMyndUploadCompleteResponse;
    spectator.component.isUploading = true;
    spectator.detectComponentChanges();

    expect(spectator.query('m-throbber')).toExist();

    const filesChangeEmitSpy = jest.spyOn(spectator.component.filesChange, 'emit');
    spectator.triggerEventHandler('m-file-upload-selector', 'completeItem', fileUploadResponse);

    expect(spectator.query('m-throbber')).not.toExist();
    expect(spectator.query('m-table')).not.toExist();
    expect(filesChangeEmitSpy).not.toHaveBeenCalled();
  });

  it('should hide loader and show error toast if file info parsing is failed', () => {
    const fileUploadResponse = { response: file } as unknown as IMyndUploadCompleteResponse;
    spectator.component.isUploading = true;
    spectator.detectComponentChanges();

    expect(spectator.query('m-throbber')).toExist();

    const errorToastSpy = jest.spyOn(spectator.component['toastrService'], 'error');
    spectator.triggerEventHandler('m-file-upload-selector', 'completeItem', fileUploadResponse);

    expect(spectator.query('m-throbber')).not.toExist();
    expect(spectator.query('m-table')).not.toExist();
    expect(errorToastSpy).toHaveBeenCalledWith('Unable to upload attachment');
  });

  it('should remove file from table, emit files change event', () => {
    const fileUploadResponse = { response: JSON.stringify(file) } as unknown as IMyndUploadCompleteResponse;
    spectator.triggerEventHandler('m-file-upload-selector', 'completeItem', fileUploadResponse);

    expect(spectator.queryAll('m-table-row')[1]).toContainText(file.fileName);

    spectator.click('m-button[icon="trash"]');
    spectator.click(spectator.query(byTestId('confirm-button'), { root: true }));
    spectator.detectComponentChanges();

    spectator.component.filesChange.pipe(take(1)).subscribe((files) => {
      expect(files).toStrictEqual([]);
      expect(spectator.query('m-table')).not.toExist();
    });
  });
});
