{"name": "service-requests-shared-dashboard", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "prefix": "lib", "projectType": "library", "sourceRoot": "packages/service-requests/shared-dashboard/src", "tags": [], "targets": {"test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "packages/service-requests/shared-dashboard/jest.config.ts"}}, "lint": {"executor": "@nx/eslint:lint"}}}