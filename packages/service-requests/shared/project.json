{"name": "service-requests-shared", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "packages/service-requests/shared/src", "prefix": "lib", "projectType": "library", "tags": [], "targets": {"test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "packages/service-requests/shared/jest.config.ts"}}, "lint": {"executor": "@nx/eslint:lint"}}}