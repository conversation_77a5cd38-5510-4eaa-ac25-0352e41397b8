{"name": "owner-newsletter", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "packages/owner-newsletter/src", "prefix": "lib", "projectType": "library", "tags": [], "targets": {"test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "packages/owner-newsletter/jest.config.ts"}}, "lint": {"executor": "@nx/eslint:lint"}}}