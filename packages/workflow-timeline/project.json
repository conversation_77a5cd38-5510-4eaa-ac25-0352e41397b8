{"name": "workflow-timeline", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "packages/workflow-timeline/src", "prefix": "lib", "projectType": "library", "tags": [], "targets": {"test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "packages/workflow-timeline/jest.config.ts"}}, "lint": {"executor": "@nx/eslint:lint"}}}