{"name": "api-service-requests", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "packages/api/service-requests/src", "prefix": "lib", "projectType": "library", "tags": [], "targets": {"test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "packages/api/service-requests/jest.config.ts"}}, "lint": {"executor": "@nx/eslint:lint"}}}