{"name": "api-in-house-tech", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "packages/api/in-house-tech/src", "prefix": "lib", "projectType": "library", "tags": [], "targets": {"test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "packages/api/in-house-tech/jest.config.ts"}}, "lint": {"executor": "@nx/eslint:lint"}}}