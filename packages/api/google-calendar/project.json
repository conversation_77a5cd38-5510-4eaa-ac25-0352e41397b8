{"name": "api-google-calendar", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "packages/api/google-calendar/src", "prefix": "lib", "projectType": "library", "tags": [], "targets": {"test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "packages/api/google-calendar/jest.config.ts"}}, "lint": {"executor": "@nx/eslint:lint"}}}