{"name": "api-price-book-entry", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "packages/api/price-book-entry/src", "prefix": "lib", "projectType": "library", "tags": [], "targets": {"test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "packages/api/price-book-entry/jest.config.ts"}}, "lint": {"executor": "@nx/eslint:lint"}}}