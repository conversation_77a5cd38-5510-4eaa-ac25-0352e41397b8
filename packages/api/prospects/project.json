{"name": "api-prospects", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "packages/api/prospects/src", "prefix": "lib", "projectType": "library", "tags": [], "targets": {"test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "packages/api/prospects/jest.config.ts"}}, "lint": {"executor": "@nx/eslint:lint"}}}