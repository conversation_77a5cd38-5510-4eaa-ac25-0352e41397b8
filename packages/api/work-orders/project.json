{"name": "api-work-orders", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "packages/api/work-orders/src", "prefix": "lib", "projectType": "library", "tags": [], "targets": {"test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "packages/api/work-orders/jest.config.ts"}}, "lint": {"executor": "@nx/eslint:lint"}}}