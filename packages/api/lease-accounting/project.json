{"name": "api-lease-accounting", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "packages/api/lease-accounting/src", "prefix": "lib", "projectType": "library", "tags": [], "targets": {"test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "packages/api/lease-accounting/jest.config.ts"}}, "lint": {"executor": "@nx/eslint:lint"}}}