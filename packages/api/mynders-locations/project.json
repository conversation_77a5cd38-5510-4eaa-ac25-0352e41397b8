{"name": "api-mynders-locations", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "packages/api/mynders-locations/src", "prefix": "lib", "projectType": "library", "tags": [], "targets": {"test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "packages/api/mynders-locations/jest.config.ts"}}, "lint": {"executor": "@nx/eslint:lint"}}}