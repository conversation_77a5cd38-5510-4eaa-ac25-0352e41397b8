import { MyndActionType } from '@myndmanagement/api-actions';

import { IMyndSubtask } from './subtask.interface';

export enum MyndSubtaskPriority {
  Low = 'LOW',
  Medium = 'MEDIUM',
  High = 'HIGH',
}

export enum MyndSubtaskEstimatedTimeType {
  FixedMinutes = 'FIXED_MINUTES',
  UnitSqFt = 'UNIT_SQFT',
  SrLineItemsCount = 'SERVICE_REQUEST_LINE_ITEMS_COUNT',
}

export interface IMyndFieldTaskConfiguration {
  priority?: MyndSubtaskPriority;
  estimatedTimeMinutes?: number;
  estimatedTimeType?: MyndSubtaskEstimatedTimeType;
  primaryAssigneeRole?: string;
  slaShiftDays?: number;
  slaBaseDateType?: string;
}

export interface IMyndSubtaskInTemplateForSave extends IMyndSubtask {
  defaultRole?: string;
  fieldTaskConfiguration?: IMyndFieldTaskConfiguration;
}

export interface IMyndSubtaskTemplateForSave {
  templateId?: string;
  title: string;
  description?: string;
  subTaskTemplates?: IMyndSubtaskInTemplateForSave[];
  active?: boolean;
  actionTypes: MyndActionType[];
  workflowTypeName?: string;
}

export interface IMyndTemplateValidationResponse {
  warnings: {
    message: string;
  }[];
}

export interface IMyndSubtaskTemplate {
  active: boolean;
  templateId: string;
  title: string;
  description?: string;
  workflowType: string;
}

export interface IMyndFollowUpTask {
  title: string;
  dueDate: string;
  assignedBoUserId: string;
  subTaskTemplateId: string;
  actionId: string;
  originalSubTaskId: string;
  comment: string;
  attachmentFileIds: string[];
}
