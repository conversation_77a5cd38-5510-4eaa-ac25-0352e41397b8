{"name": "api-owner-contributions", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "packages/api/owner-contributions/src", "prefix": "lib", "projectType": "library", "tags": [], "targets": {"test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "packages/api/owner-contributions/jest.config.ts"}}, "lint": {"executor": "@nx/eslint:lint"}}}