{"name": "api-attachments", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "packages/api/attachments/src", "prefix": "lib", "projectType": "library", "tags": [], "targets": {"test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "packages/api/attachments/jest.config.ts"}}, "lint": {"executor": "@nx/eslint:lint"}}}