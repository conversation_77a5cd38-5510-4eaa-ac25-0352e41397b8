{"name": "api-properties", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "packages/api/properties/src", "prefix": "lib", "projectType": "library", "tags": [], "targets": {"test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "packages/api/properties/jest.config.ts"}}, "lint": {"executor": "@nx/eslint:lint"}}}