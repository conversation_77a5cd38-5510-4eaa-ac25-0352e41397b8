{"name": "api-taxonomy-rules", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "packages/api/taxonomy-rules/src", "prefix": "lib", "projectType": "library", "tags": [], "targets": {"test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "packages/api/taxonomy-rules/jest.config.ts"}}, "lint": {"executor": "@nx/eslint:lint"}}}