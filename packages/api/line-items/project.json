{"name": "api-line-items", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "packages/api/line-items/src", "prefix": "lib", "projectType": "library", "tags": [], "targets": {"test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "packages/api/line-items/jest.config.ts"}}, "lint": {"executor": "@nx/eslint:lint"}}}