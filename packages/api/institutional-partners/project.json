{"name": "api-institutional-partners", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "packages/api/institutional-partners/src", "prefix": "lib", "projectType": "library", "tags": [], "targets": {"test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "packages/api/institutional-partners/jest.config.ts"}}, "lint": {"executor": "@nx/eslint:lint"}}}