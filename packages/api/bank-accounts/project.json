{"name": "api-bank-accounts", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "packages/api/bank-accounts/src", "prefix": "lib", "projectType": "library", "tags": [], "targets": {"test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "packages/api/bank-accounts/jest.config.ts"}}, "lint": {"executor": "@nx/eslint:lint"}}}