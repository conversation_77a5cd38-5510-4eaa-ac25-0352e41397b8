{"name": "api-service-request-stores", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "packages/api/service-request-stores/src", "prefix": "lib", "projectType": "library", "tags": [], "targets": {"test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "packages/api/service-request-stores/jest.config.ts"}}, "lint": {"executor": "@nx/eslint:lint"}}}