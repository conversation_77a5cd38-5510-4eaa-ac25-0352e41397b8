{"name": "api-google-spreadsheet", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "packages/api/google-spreadsheet/src", "prefix": "lib", "projectType": "library", "tags": [], "targets": {"test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "packages/api/google-spreadsheet/jest.config.ts"}}, "lint": {"executor": "@nx/eslint:lint"}}}