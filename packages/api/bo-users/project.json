{"name": "api-bo-users", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "packages/api/bo-users/src", "prefix": "lib", "projectType": "library", "tags": [], "targets": {"test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "packages/api/bo-users/jest.config.ts"}}, "lint": {"executor": "@nx/eslint:lint"}}}