{"name": "api-activity-feed", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "packages/api/activity-feed/src", "prefix": "lib", "projectType": "library", "tags": [], "targets": {"test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "packages/api/activity-feed/jest.config.ts"}}, "lint": {"executor": "@nx/eslint:lint"}}}