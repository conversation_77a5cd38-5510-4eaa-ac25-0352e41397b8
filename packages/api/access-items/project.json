{"name": "api-access-items", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "packages/api/access-items/src", "prefix": "lib", "projectType": "library", "tags": [], "targets": {"test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "packages/api/access-items/jest.config.ts"}}, "lint": {"executor": "@nx/eslint:lint"}}}