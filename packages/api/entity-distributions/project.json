{"name": "api-entity-distributions", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "packages/api/entity-distributions/src", "prefix": "lib", "projectType": "library", "tags": [], "targets": {"test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "packages/api/entity-distributions/jest.config.ts"}}, "lint": {"executor": "@nx/eslint:lint"}}}