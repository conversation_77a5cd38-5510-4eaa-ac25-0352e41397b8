{"name": "api-recurring-transactions", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "packages/api/recurring-transactions/src", "prefix": "lib", "projectType": "library", "tags": [], "targets": {"test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "packages/api/recurring-transactions/jest.config.ts"}}, "lint": {"executor": "@nx/eslint:lint"}}}