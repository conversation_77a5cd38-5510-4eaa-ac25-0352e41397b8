{"name": "api-charge-codes", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "packages/api/charge-codes/src", "prefix": "lib", "projectType": "library", "tags": [], "targets": {"test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "packages/api/charge-codes/jest.config.ts"}}, "lint": {"executor": "@nx/eslint:lint"}}}