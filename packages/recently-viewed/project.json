{"name": "recently-viewed", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "packages/recently-viewed/src", "prefix": "lib", "projectType": "library", "tags": [], "targets": {"test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "packages/recently-viewed/jest.config.ts"}}, "lint": {"executor": "@nx/eslint:lint"}}}