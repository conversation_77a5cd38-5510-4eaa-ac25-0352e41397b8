export enum MyndPermission {
  AdjustReceipt = 'ADJUST_RECEIPT',
  AmDevOps = 'AM_DEV_OPS',
  AnonymizePersonalData = 'ANONYMIZE_PERSONAL_DATA',
  ApplicationsBatchEditing = 'APPLICATIONS_BATCH_EDITING',
  ApproveWorkOrders = 'APPROVE_WORK_ORDERS',
  BatchChangeTaskStatus = 'BATCH_CHANGE_TASK_STATUS',
  BatchModifyTasks = 'BATCH_MODIFY_TASKS',
  BulkPublishUnits = 'BULK_PUBLISH_UNITS',
  CaseManagementAll = 'CASE_MANAGEMENT_ALL',
  CaseManagementBatchEdit = 'CASE_MANAGEMENT_BATCH_EDIT',
  CaseManagementBulkCreate = 'CASE_MANAGEMENT_BULK_CREATE',
  CaseManagementModifyCaseTagConsolidation = 'CASE_MANAGEMENT_MODIFY_CASE_TAG_CONSOLIDATION',
  CaseManagementRead = 'CASE_MANAGEMENT_READ',
  CaseManagementViewCaseTagConsolidation = 'CASE_MANAGEMENT_VIEW_CASE_TAG_CONSOLIDATION',
  CaseManagementWrite = 'CASE_MANAGEMENT_WRITE',
  CreateBoUsers = 'CREATE_BO_USERS',
  UpdateBoUserPhone = 'UPDATE_BO_USER_PHONE',
  CreateRoutes = 'CREATE_ROUTES',
  DeleteAccountingJournal = 'DELETE_ACCOUNTING_JOURNAL',
  DeleteAccounts = 'DELETE_ACCOUNTS',
  DeleteBoUsers = 'DELETE_BO_USERS',
  DeleteChargeCodes = 'DELETE_CHARGE_CODES',
  DeleteCharges = 'DELETE_CHARGES',
  DeleteInvoices = 'DELETE_INVOICES',
  DeletePayments = 'DELETE_PAYMENTS',
  DeleteReceipts = 'DELETE_RECEIPTS',
  DeleteTransfers = 'DELETE_TRANSFERS',
  DownloadManualCheckPdfs = 'DOWNLOAD_MANUAL_CHECK_PDFS',
  EditMessageTemplates = 'EDIT_MESSAGE_TEMPLATES',
  ExecuteRingcentralSync = 'EXECUTE_RINGCENTRAL_SYNC',
  GenerateAccessCode = 'GENERATE_ACCESS_CODE',
  ImportRawData = 'IMPORT_RAW_DATA',
  ManageInvoiceAccessRights = 'MANAGE_INVOICE_ACCESS_RIGHTS',
  ManageSudo = 'MANAGE_SUDO',
  ManageTaskTemplates = 'MANAGE_TASK_TEMPLATES',
  Modify1099TaxImport = 'MODIFY_1099_TAX_IMPORT',
  ModifyAccountingJournal = 'MODIFY_ACCOUNTING_JOURNAL',
  ModifyAccountingJournalAtEl = 'MODIFY_ACCOUNTING_JOURNAL_AT_EL',
  ModifyAccountingSettings = 'MODIFY_ACCOUNTING_SETTINGS',
  ModifyAccounts = 'MODIFY_ACCOUNTS',
  ModifyAccountsCategories = 'MODIFY_ACCOUNTS_CATEGORIES',
  ModifyAcl = 'MODIFY_ACL',
  ModifyActions = 'MODIFY_ACTIONS',
  ModifyAssets = 'MODIFY_ASSETS',
  ModifyAttachments = 'MODIFY_ATTACHMENTS',
  ModifyBankDeposits = 'MODIFY_BANK_DEPOSITS',
  ModifyBankReconciliations = 'MODIFY_BANK_RECONCILIATIONS',
  ModifyBankTransactions = 'MODIFY_BANK_TRANSACTIONS',
  ModifyBanners = 'MODIFY_BANNERS',
  ModifyBasicData = 'MODIFY_BASIC_DATA',
  ModifyBoUserGroups = 'MODIFY_BO_USER_GROUPS',
  ModifyChargeCodes = 'MODIFY_CHARGE_CODES',
  ModifyCharges = 'MODIFY_CHARGES',
  ModifyCheckRuns = 'MODIFY_CHECK_RUNS',
  ModifyCityRentalTaxRates = 'MODIFY_CITY_RENTAL_TAX_RATES',
  ModifyCommunities = 'MODIFY_COMMUNITIES',
  ModifyDistributions = 'MODIFY_DISTRIBUTIONS',
  ModifyDocuments = 'MODIFY_DOCUMENTS',
  ModifyEntities = 'MODIFY_ENTITIES',
  ModifyEntityBankAccount = 'MODIFY_ENTITY_BANK_ACCOUNT',
  ModifyEntitySetupInAccounting = 'MODIFY_ENTITY_SETUP_IN_ACCOUNTING',
  ModifyEntityTaxId = 'MODIFY_ENTITY_TAX_ID',
  ModifyFeatureFlags = 'MODIFY_FEATURE_FLAGS',
  ModifyFileSecurity = 'MODIFY_FILE_SECURITY',
  ModifyHoa = 'MODIFY_HOA',
  ModifyHoldbacks = 'MODIFY_HOLDBACKS',
  ModifyImports = 'MODIFY_IMPORTS',
  ModifyInvoiceTypeSettings = 'MODIFY_INVOICE_TYPE_SETTINGS',
  ModifyInvoices = 'MODIFY_INVOICES',
  ModifyLeaseAccounting = 'MODIFY_LEASE_ACCOUNTING',
  ModifyLeaseSetupInAccounting = 'MODIFY_LEASE_SETUP_IN_ACCOUNTING',
  ModifyLeaseSignersList = 'MODIFY_LEASE_SIGNERS_LIST',
  ModifyLeases = 'MODIFY_LEASES',
  ModifyManagedSinceDate = 'MODIFY_MANAGED_SINCE_DATE',
  ModifyManagerialDashboard = 'MODIFY_MANAGERIAL_DASHBOARD',
  ModifyNotes = 'MODIFY_NOTES',
  ModifyOpeningEntries = 'MODIFY_OPENING_ENTRIES',
  ModifyOpportunityPortfolio = 'MODIFY_OPPORTUNITY_PORTFOLIO',
  ModifyOpportunity = 'MODIFY_OPPORTUNITY',
  ModifyOwnerContributionRequests = 'MODIFY_OWNER_CONTRIBUTION_REQUESTS',
  ModifyOwners = 'MODIFY_OWNERS',
  ModifyOwnerRelationshipManager = 'MODIFY_OWNER_RELATIONSHIP_MANAGER',
  ModifyPayments = 'MODIFY_PAYMENTS',
  ModifyPersons = 'MODIFY_PERSONS',
  ModifyPersonsNotificationSettings = 'MODIFY_PERSONS_NOTIFICATION_SETTINGS',
  ModifyPets = 'MODIFY_PETS',
  ModifyPostingPeriods = 'MODIFY_POSTING_PERIODS',
  ModifyPropertyGroups = 'MODIFY_PROPERTY_GROUPS',
  ModifyPropertyInsurances = 'MODIFY_PROPERTY_INSURANCES',
  ModifyPropertySetupInAccounting = 'MODIFY_PROPERTY_SETUP_IN_ACCOUNTING',
  ModifyProspects = 'MODIFY_PROSPECTS',
  ModifyPyre = 'MODIFY_PYRE',
  ModifyReceiptAccount = 'MODIFY_RECEIPT_ACCOUNT',
  ModifyReceipts = 'MODIFY_RECEIPTS',
  ModifyRecurringCharges = 'MODIFY_RECURRING_CHARGES',
  ModifyRecurringInvoices = 'MODIFY_RECURRING_INVOICES',
  ModifyRentReadyReview = 'MODIFY_RENT_READY_REVIEW',
  ModifyReportingPackages = 'MODIFY_REPORTING_PACKAGES',
  ModifyReserves = 'MODIFY_RESERVES',
  ModifyResidents = 'MODIFY_RESIDENTS',
  ModifyRoles = 'MODIFY_ROLES',
  ModifyRubsProviders = 'MODIFY_RUBS_PROVIDERS',
  ModifyServiceRequests = 'MODIFY_SERVICE_REQUESTS',
  ModifyTasks = 'MODIFY_TASKS',
  ModifyThreads = 'MODIFY_THREADS',
  ModifyTrainingMaterials = 'MODIFY_TRAINING_MATERIALS',
  ModifyTransfers = 'MODIFY_TRANSFERS',
  ModifyUserCsat = 'MODIFY_USER_CSAT',
  ModifyUserCsatSettings = 'MODIFY_USER_CSAT_SETTINGS',
  ModifyUsers = 'MODIFY_USERS',
  ModifyVehicles = 'MODIFY_VEHICLES',
  ModifyVendorSetupInAccounting = 'MODIFY_VENDOR_SETUP_IN_ACCOUNTING',
  ModifyVendors = 'MODIFY_VENDORS',
  ModifyOwnerCommunicationStatus = 'MODIFY_OWNER_COMMUNICATION_STATUS',
  OxDevOps = 'OX_DEV_OPS',
  PerformDepositAccounting = 'PERFORM_DEPOSIT_ACCOUNTING',
  PostCheckRuns = 'POST_CHECK_RUNS',
  ProspectsBatchEditing = 'PROSPECTS_BATCH_EDITING',
  PublishUnits = 'PUBLISH_UNITS',
  ReindexUniversalSearchDocument = 'REINDEX_UNIVERSAL_SEARCH_DOCUMENT',
  RunReports = 'RUN_REPORTS',
  RunTuScreening = 'RUN_TU_SCREENING',
  ServerPushSubscription = 'SERVER_PUSH_SUBSCRIPTION',
  SendOwnerNPS = 'SEND_OWNER_NPS',
  UnreconcileBankTransactions = 'UNRECONCILE_BANK_TRANSACTIONS',
  VendorWorkflowAccess = 'VENDOR_WORKFLOW_ACCESS',
  View1099TaxImport = 'VIEW_1099_TAX_IMPORT',
  ViewAccountingJournal = 'VIEW_ACCOUNTING_JOURNAL',
  ViewAccountingSettings = 'VIEW_ACCOUNTING_SETTINGS',
  ViewAccounts = 'VIEW_ACCOUNTS',
  ViewAcl = 'VIEW_ACL',
  ViewActions = 'VIEW_ACTIONS',
  ViewActivityFeed = 'VIEW_ACTIVITY_FEED',
  ViewAppliedFeeCalculation = 'VIEW_APPLIED_FEE_CALCULATION',
  ViewAssets = 'VIEW_ASSETS',
  ViewAttachments = 'VIEW_ATTACHMENTS',
  ViewAudit = 'VIEW_AUDIT',
  ViewAuditHistory = 'VIEW_AUDIT_HISTORY',
  ViewAuditTrail = 'VIEW_AUDIT_TRAIL',
  ViewBalance = 'VIEW_BALANCE',
  ViewBankDeposits = 'VIEW_BANK_DEPOSITS',
  ViewBankReconciliations = 'VIEW_BANK_RECONCILIATIONS',
  ViewBankTransactions = 'VIEW_BANK_TRANSACTIONS',
  ViewBankTransactionsFile = 'VIEW_BANK_TRANSACTIONS_FILE',
  ViewBanners = 'VIEW_BANNERS',
  ViewBasicData = 'VIEW_BASIC_DATA',
  ViewBoUserGroups = 'VIEW_BO_USER_GROUPS',
  ViewBoUsers = 'VIEW_BO_USERS',
  ViewChargeCodes = 'VIEW_CHARGE_CODES',
  ViewCharges = 'VIEW_CHARGES',
  ViewCheckRuns = 'VIEW_CHECK_RUNS',
  ViewCommunities = 'VIEW_COMMUNITIES',
  ViewDistributions = 'VIEW_DISTRIBUTIONS',
  ViewDocuments = 'VIEW_DOCUMENTS',
  ViewEntities = 'VIEW_ENTITIES',
  ViewEntityBankAccount = 'VIEW_ENTITY_BANK_ACCOUNT',
  ViewEntitySetupInAccounting = 'VIEW_ENTITY_SETUP_IN_ACCOUNTING',
  ViewEntityTaxId = 'VIEW_ENTITY_TAX_ID',
  ViewFeatureFlags = 'VIEW_FEATURE_FLAGS',
  ViewHoa = 'VIEW_HOA',
  ViewHoldbacks = 'VIEW_HOLDBACKS',
  ViewImports = 'VIEW_IMPORTS',
  ViewInvoiceAccessRights = 'VIEW_INVOICE_ACCESS_RIGHTS',
  ViewInvoiceTypeSettings = 'VIEW_INVOICE_TYPE_SETTINGS',
  ViewInvoices = 'VIEW_INVOICES',
  ViewLeaseAccounting = 'VIEW_LEASE_ACCOUNTING',
  ViewLeaseSetupInAccounting = 'VIEW_LEASE_SETUP_IN_ACCOUNTING',
  ViewLeases = 'VIEW_LEASES',
  ViewManagerialDashboard = 'VIEW_MANAGERIAL_DASHBOARD',
  ViewNotes = 'VIEW_NOTES',
  ViewNotificationsLo = 'VIEW_NOTIFICATIONS_LO',
  ViewNotificationsLog = 'VIEW_NOTIFICATIONS_LOG',
  ViewOpeningEntries = 'VIEW_OPENING_ENTRIES',
  ViewOwnerContributionRequests = 'VIEW_OWNER_CONTRIBUTION_REQUESTS',
  ViewOwners = 'VIEW_OWNERS',
  ViewOwnerRelationshipManager = 'VIEW_OWNER_RELATIONSHIP_MANAGER',
  ViewPayments = 'VIEW_PAYMENTS',
  ViewPersons = 'VIEW_PERSONS',
  ViewPets = 'VIEW_PETS',
  ViewPlatformData = 'VIEW_PLATFORM_DATA',
  ViewPostingPeriods = 'VIEW_POSTING_PERIODS',
  ViewPropertyGroups = 'VIEW_PROPERTY_GROUPS',
  ViewPropertyInsurances = 'VIEW_PROPERTY_INSURANCES',
  ViewPropertySetupInAccounting = 'VIEW_PROPERTY_SETUP_IN_ACCOUNTING',
  ViewProspects = 'VIEW_PROSPECTS', // x
  ViewReceiptImports = 'VIEW_RECEIPT_IMPORTS',
  ViewReceipts = 'VIEW_RECEIPTS',
  ViewRecurringCharges = 'VIEW_RECURRING_CHARGES',
  ViewRecurringInvoices = 'VIEW_RECURRING_INVOICES',
  ViewRentReadyReview = 'VIEW_RENT_READY_REVIEW',
  ViewReportingPackages = 'VIEW_REPORTING_PACKAGES',
  ViewReserves = 'VIEW_RESERVES',
  ViewRoles = 'VIEW_ROLES',
  ViewRubsProviders = 'VIEW_RUBS_PROVIDERS',
  ViewServiceRequests = 'VIEW_SERVICE_REQUESTS',
  ViewTasks = 'VIEW_TASKS',
  ViewFieldTasksConfig = 'VIEW_FIELD_TASKS_CONFIG',
  ViewThreads = 'VIEW_THREADS',
  ViewTrainingMaterials = 'VIEW_TRAINING_MATERIALS',
  ViewTransfers = 'VIEW_TRANSFERS',
  ViewUniversalSearchResult = 'VIEW_UNIVERSAL_SEARCH_RESULT',
  ViewUserCsat = 'VIEW_USER_CSAT',
  ViewUsers = 'VIEW_USERS',
  ViewVehicles = 'VIEW_VEHICLES',
  ViewVendorSetupInAccounting = 'VIEW_VENDOR_SETUP_IN_ACCOUNTING',
  ViewVendors = 'VIEW_VENDORS',
  MoveOutBackdating  = 'MOVE_OUT_BACKDATING',
}
