{"name": "related-cases", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "packages/related-cases/src", "prefix": "lib", "projectType": "library", "tags": [], "targets": {"test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "packages/related-cases/jest.config.ts"}}, "lint": {"executor": "@nx/eslint:lint"}}}