{"name": "back-office-layout", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "packages/back-office-layout/src", "prefix": "lib", "projectType": "library", "tags": [], "targets": {"test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "packages/back-office-layout/jest.config.ts"}}, "lint": {"executor": "@nx/eslint:lint"}}}