import { MyndServiceRequestType } from '@myndmanagement/api-service-requests';
import { myndOttoAppPrefix, myndOttoLinks } from '@myndmanagement/common-otto';
import { MyndPermission } from '@myndmanagement/permissions';

import { ISideMenu } from '../interfaces/side-menu.interface';
import { sortSecondarySideMenuItems } from '../utils/sort-side-menu-items';

import {
  selfServiceConfigurationFf,
  showLrdnLinkInOttoMenuFf,
  workos2235LinkToEscalationSituationsPage,
} from './ff-keys.constant';

export const sideMenu: ISideMenu[] = sortSecondarySideMenuItems([
  {
    links: [
      {
        title: 'Control Centers',
        mboIcon: 'icon-grid-md',
        skipSecondaryLinksSorting: true,
        secondaryItems: [
          {
            href: `${myndOttoAppPrefix.backOffice}/pm-center`,
            title: 'Portfolio Manager CC',
          },
          {
            href: `${myndOttoAppPrefix.leasing}/hub`,
            title: 'Leasing CC',
          },
          {
            href: `${myndOttoAppPrefix.serviceRequests}/dashboard`,
            title: 'Service Request CC',
          },
          {
            href: `${myndOttoAppPrefix.serviceRequests}/renovation-dashboard`,
            title: 'Renovation CC',
          },
          {
            href: `${myndOttoAppPrefix.serviceRequests}/field-dispatchers-dashboard`,
            title: 'Field Dispatcher CC',
          },
          {
            href: `${myndOttoAppPrefix.serviceRequests}/trade-specialist-dashboard`,
            title: 'Trade Specialist CC',
          },
          {
            href: myndOttoAppPrefix.rxControlCenter,
            title: 'Resident Experience CC',
            featureFlag: 'rx-8940-show-rx-control-center-link',
          },
        ],
      },
      {
        title: 'Reporting',
        mboIcon: 'icon-docs',
        skipSecondaryLinksSorting: true,
        secondaryItems: [
          {
            href: `${myndOttoAppPrefix.reporting}/home`,
            title: 'Home',
          },
          {
            href: `${myndOttoAppPrefix.reporting}/catalogue`,
            title: 'Reports',
          },
          {
            href: `${myndOttoAppPrefix.reporting}/atlan`,
            title: 'Atlan',
          },
        ],
      },
      {
        href: `${myndOttoAppPrefix.caseManagement}/`,
        title: 'Case Management',
        mboIcon: 'icon-chat',
      },
      {
        href: `${myndOttoAppPrefix.taskManagement}/`,
        title: 'Task Management',
        mboIcon: 'icon-action-task-md',
      },
      {
        title: 'Workflows',
        mboIcon: 'icon-otto-md1',
        secondaryItems: [
          {
            href: myndOttoLinks.tableView('plat_OwnerCreditRequest'),
            title: 'Owner Credit Request',
          },
          {
            href: myndOttoLinks.tableView('rx_MoveOutOrchestration'),
            title: 'Move out',
          },
          {
            href: myndOttoLinks.tableView('rx_SodaCoordination'),
            title: 'Deposit Accounting',
          },
          {
            href: myndOttoLinks.tableView('constr_TurnProcessing'),
            title: 'Turn Processing',
          },
          {
            href: myndOttoLinks.tableView('plat_Escalation2023'),
            title: 'Escalations',
          },
          {
            href: myndOttoLinks.tableView('tbd_LegalDispute'),
            title: 'Legal Disputes',
          },
          {
            href: myndOttoLinks.tableView('les_VRR_Activities'),
            title: 'VRR Activities',
          },
          {
            href: myndOttoLinks.tableView('tbd_UtilitySetup'),
            title: 'Utility Setup',
          },
          {
            href: myndOttoLinks.tableView('les_Lease_Expiration'),
            title: 'Lease Expiration',
          },
          {
            href: myndOttoLinks.tableView('tbd_VacantOnboarding'),
            title: 'Vacant Onboarding',
          },
          {
            href: myndOttoLinks.tableView('tbd_Eviction'),
            title: 'Eviction',
          },
          {
            href: myndOttoLinks.tableView('rx_PostMoveIn'),
            title: 'Post Move In',
          },
          {
            href: myndOttoLinks.tableView('les_PreMarketing'),
            title: 'Lease Marketing',
          },
          {
            href: myndOttoLinks.tableView('tbd_HoaIntake'),
            title: 'HOA Intake',
          },
          {
            href: myndOttoLinks.tableView('rx_LeaseSigning'),
            title: 'Lease Signing',
          },
          {
            href: myndOttoLinks.tableView('rx_SmallBalance'),
            title: 'Small Balance Collection',
            featureFlag: 'rx-8811-enable-small-balance-wf-table-view',
          },
          {
            href: myndOttoLinks.tableView('rx_LegalRentDemandNotice'),
            title: 'Legal Rent Demand Notice',
            featureFlag: showLrdnLinkInOttoMenuFf,
          },
        ],
      },
    ],
  },
  {
    title: 'Leasing',
    links: [
      {
        title: 'Leasing and Screening',
        mboIcon: 'icon-grid-md',
        skipSecondaryLinksSorting: true,
        secondaryItems: [
          {
            href: `${myndOttoAppPrefix.leasing}/hub`,
            title: 'Control Center',
          },
          {
            href: `${myndOttoAppPrefix.leasing}/managerial-dashboard`,
            title: 'Managerial Dashboard',
            permission: MyndPermission.ViewManagerialDashboard,
          },
          {
            href: `${myndOttoAppPrefix.leasing}/prospects`,
            title: 'Prospect Management',
          },
          {
            href: `${myndOttoAppPrefix.leasing}/applications`,
            title: 'Applications',
          },
          {
            href: `${myndOttoAppPrefix.rxAutomation}/lease-creations`,
            title: 'Lease Creation',
          },
        ],
      },
      {
        title: 'Lease Marketing',
        mboIcon: 'icon-refresh-md',
        skipSecondaryLinksSorting: true,
        secondaryItems: [
          {
            href: `${myndOttoAppPrefix.leasing}/syndications`,
            title: 'Syndication Overview',
          },
          {
            href: `${myndOttoAppPrefix.leasing}/rent-ready-review`,
            title: 'Rent Ready Review',
            permission: MyndPermission.ViewRentReadyReview,
          },
        ],
      },
    ],
  },
  {
    title: 'Operations',
    links: [
      {
        title: 'Onboarding',
        mboIcon: 'icon-rocket',
        secondaryItems: [
          {
            href: `${myndOttoAppPrefix.backOffice}/data-import`,
            title: 'Data Import',
          },
          {
            href: `${myndOttoAppPrefix.backOffice}/owners/invite`,
            title: 'Create Owner',
          },
          {
            href: `${myndOttoAppPrefix.backOffice}/entities/create`,
            title: 'Create Entity',
          },
          {
            href: `${myndOttoAppPrefix.backOffice}/properties/create`,
            title: 'Create Property',
          },
          {
            href: `${myndOttoAppPrefix.backOffice}/owner-opportunities`,
            title: 'Opportunities',
          },
          {
            href: `${myndOttoAppPrefix.backOffice}/owner-onboarding`,
            title: 'Retail Control Center',
          },
          {
            href: `${myndOttoAppPrefix.backOffice}/institutional-onboarding`,
            title: 'Institutional Control Center',
          },
          {
            href: `${myndOttoAppPrefix.backOffice}/am-oa-mapping`,
            title: 'AM to OA User Mapping',
          },
          {
            href: `${myndOttoAppPrefix.hoa}/hoas`,
            title: 'HOA Control Center',
          },
        ],
      },
      {
        title: 'Residents',
        mboIcon: 'icon-residents',
        secondaryItems: [
          {
            href: myndOttoLinks.tableView('rx_SodaCoordination'),
            title: 'Deposit Accounting',
          },
          {
            href: `${myndOttoAppPrefix.rxAutomation}/lease-expirations/`,
            title: 'Lease Expiration',
          },
        ],
      },
      {
        title: 'Owners',
        mboIcon: 'icon-entity',
        secondaryItems: [
          {
            href: `${myndOttoAppPrefix.backOffice}/report-proxy/Directories`,
            title: 'Directories',
          },
          {
            href: `${myndOttoAppPrefix.backOffice}/nps-campaigns`,
            title: 'NPS Campaigns',
          },
        ],
      },
      {
        title: 'iPartners',
        mboIcon: 'icon-institutional',
        href: `${myndOttoAppPrefix.backOffice}/institutional-partners`,
      },
    ],
  },
  {
    title: 'Maintenance',
    links: [
      {
        href: myndOttoAppPrefix.serviceRequests,
        title: 'Service Requests',
        mboIcon: 'icon-wrench',
        secondaryItems: [
          {
            href: `${myndOttoAppPrefix.serviceRequests}/?defaultType=${MyndServiceRequestType.ServiceRequest}`,
            title: 'Service Requests',
          },
          {
            href: `${myndOttoAppPrefix.serviceRequests}/?defaultType=${MyndServiceRequestType.Turn}`,
            title: 'Turnover',
          },
          {
            href: `${myndOttoAppPrefix.serviceRequests}/?defaultType=${MyndServiceRequestType.Renovation}`,
            title: 'Renovations',
          },
          {
            href: `${myndOttoAppPrefix.serviceRequests}/?defaultType=${MyndServiceRequestType.Inspection}`,
            title: 'Inspections',
          },
          {
            href: `${myndOttoAppPrefix.serviceRequests}/?defaultType=${MyndServiceRequestType.Recurring}`,
            title: 'Recurring SR',
          },
          {
            href: `${myndOttoAppPrefix.serviceRequests}/recurring-service-request`,
            title: 'Manage Recurring SR',
          },
          {
            href: `${myndOttoAppPrefix.serviceRequests}/dashboard`,
            title: 'Control Center',
          },
        ],
      },
      {
        href: `${myndOttoAppPrefix.serviceRequests}/work-orders`,
        title: 'Work Order Approvals',
        mboIcon: 'icon-tools-md',
      },
      {
        href: `${myndOttoAppPrefix.serviceRequests}/supplyware-transactions`,
        title: 'Supplyware transactions',
        mboIcon: 'icon-operations-md',
      },
      {
        href: myndOttoAppPrefix.vendors,
        title: 'Vendors',
        mboIcon: 'icon-vendors',
      },
      {
        href: `${myndOttoAppPrefix.serviceRequests}/price-book`,
        title: 'Line Items library',
        mboIcon: 'icon-audit-trail-md',
      },
    ],
  },
  {
    title: 'Accounting',
    links: [
      {
        title: 'Transactions',
        mboIcon: 'icon-transactions',
        secondaryItems: [
          {
            href: `${myndOttoAppPrefix.billingAndPayments}/charges`,
            title: 'Charges',
          },
          {
            href: `${myndOttoAppPrefix.accounting}/transactions`,
            title: 'Export / Import',
          },
          {
            href: `${myndOttoAppPrefix.billingAndPayments}/invoices`,
            title: 'Invoices',
          },
          {
            href: `${myndOttoAppPrefix.accounting}/journal-entries`,
            title: 'Journal Entries',
          },
          {
            href: `${myndOttoAppPrefix.billingAndPayments}/payments`,
            title: 'Payments',
          },
          {
            href: `${myndOttoAppPrefix.billingAndPayments}/receipts`,
            title: 'Receipts',
          },
          {
            href: `${myndOttoAppPrefix.billingAndPayments}/security-deposit-credits`,
            title: 'Security Deposit Credits',
          },
          {
            href: `${myndOttoAppPrefix.billingAndPayments}/credits`,
            title: 'Credits',
          },
          {
            href: `${myndOttoAppPrefix.billingAndPayments}/concessions`,
            title: 'Concessions',
          },
          {
            href: `${myndOttoAppPrefix.accounting}/transfers`,
            title: 'Transfers',
          },
          {
            href: `${myndOttoAppPrefix.accounting}/bank-deposits`,
            title: 'Bank Deposits',
          },
        ],
      },
      {
        title: 'Operational',
        mboIcon: 'icon-operations',
        secondaryItems: [
          {
            href: `${myndOttoAppPrefix.billingAndPayments}/monthly-billing`,
            title: 'Monthly Billing',
          },
          {
            href: `${myndOttoAppPrefix.billingAndPayments}/check-runs`,
            title: 'Check Runs',
          },
          {
            href: `${myndOttoAppPrefix.accounting}/posting-periods`,
            title: 'Posting Periods',
          },
          {
            href: `${myndOttoAppPrefix.accounting}/bank-rec`,
            title: 'Bank Reconciliation',
          },
          {
            href: `${myndOttoAppPrefix.accounting}/distributions`,
            title: 'Distributions',
          },
          {
            href: `${myndOttoAppPrefix.accounting}/entity-distributions`,
            title: 'Entity Distributions',
          },
          {
            href: `${myndOttoAppPrefix.accounting}/distributions/sd-true-ups`,
            title: 'SD True Ups',
          },
          {
            href: `${myndOttoAppPrefix.accounting}/holdbacks`,
            title: 'Holdbacks',
          },
          {
            href: `${myndOttoAppPrefix.backOffice}/eom-reports`,
            title: 'EOM Reports',
          },
          {
            href: `${myndOttoAppPrefix.billingAndPayments}/property-fees`,
            title: 'Property Fees',
          },
          {
            href: `${myndOttoAppPrefix.billingAndPayments}/recurring-invoices`,
            title: 'Recurring Invoices',
          },
          {
            href: `${myndOttoAppPrefix.billingAndPayments}/mynd-fee-flows`,
            title: 'Mynd Fee Flows',
          },
          {
            href: `${myndOttoAppPrefix.accounting}/fixed-assets`,
            title: 'Fixed Assets',
          },
        ],
      },
      {
        title: 'Setup & Tools',
        mboIcon: 'icon-accounting-setup',
        secondaryItems: [
          {
            href: `${myndOttoAppPrefix.accounting}/chart-of-gl-accounts`,
            title: 'Chart of Accounts',
          },
          {
            href: `${myndOttoAppPrefix.accounting}/bank-accounts`,
            title: 'Bank Accounts',
          },
          {
            href: `${myndOttoAppPrefix.billingAndPayments}/charge-codes`,
            title: 'Charge Codes',
          },
          {
            href: `${myndOttoAppPrefix.reporting}/packages`,
            title: 'Report Packages',
          },
          {
            href: `${myndOttoAppPrefix.reporting}/settings`,
            title: 'Exception Report Settings',
          },
          {
            href: `${myndOttoAppPrefix.accounting}/export-import`,
            title: 'Export / Import',
          },
          {
            href: `${myndOttoAppPrefix.billingAndPayments}/tax-form-import`,
            title: '1099 Tax Import',
          },
          {
            href: `${myndOttoAppPrefix.backOffice}/entities/accounting/import`,
            title: 'Import Tax Ids',
          },
          {
            href: `${myndOttoAppPrefix.backOffice}/1099-reports`,
            title: '1099 Year End Reports',
          },
          {
            href: `${myndOttoAppPrefix.billingAndPayments}/city-rental-tax-rates`,
            title: 'City Rental Tax Rates',
          },
        ],
      },
    ],
  },
  {
    title: 'Global',
    links: [
      {
        title: 'Settings',
        mboIcon: 'icon-settings',
        secondaryItems: [
          {
            href: `${myndOttoAppPrefix.accounting}/settings`,
            title: 'Accounting Settings',
          },
          {
            href: `${myndOttoAppPrefix.administration}/property-services-settings`,
            title: 'Property Services Settings',
          },
          {
            href: `${myndOttoAppPrefix.taskManagement}/templates`,
            title: 'Task Templates',
            permission: MyndPermission.ViewTasks,
          },
          {
            href: `${myndOttoAppPrefix.taskManagement}/field-tasks-config/all`,
            title: 'Field Tasks Configuration',
            permission: MyndPermission.ViewFieldTasksConfig,
          },
          {
            href: `${myndOttoAppPrefix.caseManagement}/message-templates`,
            title: 'Message Templates',
            permission: MyndPermission.EditMessageTemplates,
          },
          {
            href: `${myndOttoAppPrefix.rxAutomation}/lease-creations/lease-signers`,
            title: 'Lease Signers',
            permission: MyndPermission.ModifyLeaseSignersList,
          },
          {
            href: `${myndOttoAppPrefix.leasing}/fraudsters`,
            title: 'Fraud Blocklist',
            permission: MyndPermission.ViewProspects,
          },
          {
            href: `${myndOttoAppPrefix.administration}/tag-management`,
            title: 'Tag Management',
          },
          {
            href: `${myndOttoAppPrefix.administration}/roles-matrix`,
            title: 'Roles Matrix',
          },
          {
            href: `${myndOttoAppPrefix.administration}/self-service-configuration`,
            title: 'Self Service Configuration',
            featureFlag: selfServiceConfigurationFf,
          },
          {
            href: `${myndOttoAppPrefix.administration}/portfolio-management`,
            title: 'Portfolio',
          },
          {
            href: `${myndOttoAppPrefix.taskManagement}/escalation/situations`,
            title: 'Escalation Situations',
            featureFlag: workos2235LinkToEscalationSituationsPage,
          },
          {
            href: `${myndOttoAppPrefix.backOffice}/settings/rental-registration/rules`,
            title: 'Rental Registration Rules',
          },
          {
            href: `${myndOttoAppPrefix.serviceRequests}/taxonomy-mapping-configuration`,
            title: 'Tech Taxonomy Mapping',
          },
          {
            href: `${myndOttoAppPrefix.workflows}/wf-training-config`,
            title: 'Workflow Training Config',
          },
        ],
      },
      {
        title: 'User Management',
        mboIcon: 'icon-support',
        secondaryItems: [
          {
            href: `${myndOttoAppPrefix.administration}/users`,
            title: 'Users',
          },
          {
            href: `${myndOttoAppPrefix.administration}/groups`,
            title: 'Groups',
          },
          {
            href: `${myndOttoAppPrefix.administration}/roles`,
            title: 'Roles',
          },
          {
            href: `${myndOttoAppPrefix.billingAndPayments}/user-access`,
            title: 'Invoice Permissions',
          },
        ],
      },
    ],
  },
]);
