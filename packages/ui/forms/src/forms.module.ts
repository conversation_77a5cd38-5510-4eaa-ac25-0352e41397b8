import { A11yModule } from '@angular/cdk/a11y';
import { OverlayModule } from '@angular/cdk/overlay';
import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MyndCommonModule } from '@myndmanagement/common';
import { MyndTooltipModule } from '@myndmanagement/ui-tooltip';
import { NgxMaskDirective, NgxMaskPipe, provideNgxMask } from 'ngx-mask';

import { MyndAutocompleteComponent } from './components/autocomplete/autocomplete.component';
import {
  MyndAutocompleteMultiselectComponent,
} from './components/autocomplete-multiselect/autocomplete-multiselect.component';
import { MyndAutocompleteSelectComponent } from './components/autocomplete-select/autocomplete-select.component';
import { MyndBoolSelectComponent } from './components/bool-select/bool-select.component';
import { MyndButtonComponent } from './components/button/button.component';
import { MyndCalendarComponent } from './components/calendar/calendar.component';
import { MyndCalendarMonthComponent } from './components/calendar-month/calendar-month.component';
import { MyndCalendarMonthGridComponent } from './components/calendar-month-grid/calendar-month-grid.component';
import { MyndCalendarYearGridComponent } from './components/calendar-year-grid/calendar-year-grid.component';
import { MyndCheckboxComponent } from './components/checkbox/checkbox.component';
import { MyndCheckboxGroupComponent } from './components/checkbox-group/checkbox-group.component';
import { MyndDateInputComponent } from './components/date-input/date-input.component';
import { MyndDateMonthPickerComponent } from './components/date-month-picker/date-month-picker.component';
import { MyndDatePickerComponent } from './components/date-picker/date-picker.component';
import { MyndDateRangePickerComponent } from './components/date-range-picker/date-range-picker.component';
import {
  MyndDayOfMonthMultiselectComponent,
} from './components/day-of-month-multiselect/day-of-month-multiselect.component';
import { MyndDropdownComponent } from './components/dropdown/dropdown.component';
import { MyndDropdownOptionComponent } from './components/dropdown-option/dropdown-option.component';
import { MyndFeedbackBooleanComponent } from './components/feedback-boolean/feedback-boolean.component';
import { MyndFeedbackRatingComponent } from './components/feedback-rating/feedback-rating.component';
import { MyndFeedbackStarsComponent } from './components/feedback-stars/feedback-stars.component';
import { MyndFormGroupComponent } from './components/form-group/form-group.component';
import { MyndIconSelectComponent } from './components/icon-select/icon-select.component';
import { MyndInputFooterComponent } from './components/input-footer/input-footer.component';
import { MyndLabelComponent } from './components/label/label.component';
import { MyndLabelActionComponent } from './components/label-action/label-action.component';
import {
  MyndLabelActionSeparatorComponent,
} from './components/label-action-separator/label-action-separator.component';
import { MyndMentionsComponent } from './components/mentions/mentions.component';
import { MyndMultiselectComponent } from './components/multiselect/multiselect.component';
import { MyndNumberInputComponent } from './components/number-input/number-input.component';
import { MyndOptionComponent } from './components/option/option.component';
import { MyndOptionsContainerComponent } from './components/options-container/options-container.component';
import { MyndRadioButtonComponent } from './components/radio-button/radio-button.component';
import { MyndRadioButtonGroupComponent } from './components/radio-button-group/radio-button-group.component';
import { MyndReadOnlyComponent } from './components/read-only/read-only.component';
import { MyndRichEditorComponent } from './components/rich-editor/rich-editor.component';
import { MyndSelectComponent } from './components/select/select.component';
import { MyndSliderComponent } from './components/slider/slider.component';
import { MyndSsnFieldComponent } from './components/ssn-field/ssn-field.component';
import { MyndSwitchComponent } from './components/switch/switch.component';
import { MyndTabbedOptionComponent } from './components/tabbed-option/tabbed-option.component';
import { MyndTabbedOptionGroupComponent } from './components/tabbed-option-group/tabbed-option-group.component';
import { MyndTagsComponent } from './components/tags/tags.component';
import { MyndTagsMobileComponent } from './components/tags-mobile/tags-mobile.component';
import { MyndTextAreaComponent } from './components/text-area/text-area.component';
import { MyndTextInputComponent } from './components/text-input/text-input.component';
import { MyndTimeInputComponent } from './components/time-input/time-input.component';
import { MyndTimePickerComponent } from './components/time-picker/time-picker.component';
import { MyndTimeSlotPickerComponent } from './components/time-slot-picker/time-slot-picker.component';
import { MyndTimeSlotsDropdownComponent } from './components/time-slots-dropdown/time-slots-dropdown.component';
import { MyndToggleButtonGroupComponent } from './components/toggle-button-group/toggle-button-group.component';
import { MyndFormDisabledDirective } from './directives/form-disabled.directive';
import { MyndResetFormValidationDirective } from './directives/reset-form-validation.directive';
import { MyndMainFormDirective, MyndValidateControlDirective } from './directives/validate-control.directive';
import { MyndBoolDisplayValuePipe } from './pipes/bool-display-value.pipe';
import { MyndGetOptionKeyPipe } from './pipes/get-option-key.pipe';
import { MyndGetOptionValuePipe } from './pipes/get-option-value.pipe';
import { MyndMapValuePipe } from './pipes/map-value.pipe';
import { MyndTimeslotToDatePipe } from './pipes/timeslot-to-date.pipe';

const components = [
  MyndAutocompleteComponent,
  MyndAutocompleteSelectComponent,
  MyndAutocompleteMultiselectComponent,
  MyndBoolSelectComponent,
  MyndButtonComponent,
  MyndCalendarComponent,
  MyndCalendarMonthComponent,
  MyndCalendarMonthGridComponent,
  MyndCalendarYearGridComponent,
  MyndCheckboxGroupComponent,
  MyndCheckboxComponent,
  MyndDateInputComponent,
  MyndDateMonthPickerComponent,
  MyndDatePickerComponent,
  MyndDateRangePickerComponent,
  MyndDayOfMonthMultiselectComponent,
  MyndDropdownComponent,
  MyndDropdownOptionComponent,
  MyndFeedbackBooleanComponent,
  MyndFeedbackRatingComponent,
  MyndFeedbackStarsComponent,
  MyndFormGroupComponent,
  MyndIconSelectComponent,
  MyndLabelActionSeparatorComponent,
  MyndMentionsComponent,
  MyndMultiselectComponent,
  MyndNumberInputComponent,
  MyndOptionComponent,
  MyndOptionsContainerComponent,
  MyndRadioButtonComponent,
  MyndRadioButtonGroupComponent,
  MyndReadOnlyComponent,
  MyndRichEditorComponent,
  MyndSelectComponent,
  MyndSliderComponent,
  MyndSsnFieldComponent,
  MyndSwitchComponent,
  MyndTabbedOptionComponent,
  MyndTabbedOptionGroupComponent,
  MyndTagsComponent,
  MyndTagsMobileComponent,
  MyndTextInputComponent,
  MyndTimeInputComponent,
  MyndTimePickerComponent,
  MyndTimeSlotPickerComponent,
  MyndTimeSlotsDropdownComponent,
  MyndToggleButtonGroupComponent,
];
const standaloneComponents = [
  MyndInputFooterComponent,
  MyndLabelActionComponent,
  MyndLabelComponent,
  MyndTextAreaComponent,
];

const directives = [
  MyndResetFormValidationDirective,
  MyndValidateControlDirective,
  MyndFormDisabledDirective,
  MyndMainFormDirective,
];
const pipes = [
  MyndBoolDisplayValuePipe,
  MyndGetOptionKeyPipe,
  MyndGetOptionValuePipe,
  MyndMapValuePipe,
  MyndTimeslotToDatePipe,
];

@NgModule({
  imports: [
    CommonModule,
    OverlayModule,
    A11yModule,
    FormsModule,
    ReactiveFormsModule,
    MyndCommonModule,
    MyndTooltipModule,
    NgxMaskDirective,
    NgxMaskPipe,
    ...standaloneComponents,
  ],
  declarations: [
    ...components,
    ...directives,
    ...pipes,
  ],
  exports: [
    NgxMaskDirective,
    NgxMaskPipe,
    ...components,
    ...standaloneComponents,
    ...directives,
    ...pipes,
  ],
  providers: [
    provideNgxMask(),
    ...pipes,
  ],
})
export class MyndFormsModule {}
