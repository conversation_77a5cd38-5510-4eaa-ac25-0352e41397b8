import { EventEmitter } from '@angular/core';
import { ReactiveFormsModule, UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { MyndCommonModule } from '@myndmanagement/common';
import { MyndTooltipModule } from '@myndmanagement/ui-tooltip';
import { SpectatorDirective, createDirectiveFactory } from '@ngneat/spectator/jest';

import { MyndInputFooterComponent } from '../components/input-footer/input-footer.component';
import { MyndLabelComponent } from '../components/label/label.component';
import { MyndLabelActionComponent } from '../components/label-action/label-action.component';
import { MyndTextInputComponent } from '../components/text-input/text-input.component';

import { MyndResetFormValidationDirective } from './reset-form-validation.directive';
import { MyndValidateControlDirective } from './validate-control.directive';

describe('MyndResetFormValidationDirective', () => {
  let spectator: SpectatorDirective<
    MyndResetFormValidationDirective,
    { resetSource: EventEmitter<unknown>; form: UntypedFormGroup }
  >;

  const createDirective = createDirectiveFactory({
    directive: MyndResetFormValidationDirective,
    imports: [
      ReactiveFormsModule,
      MyndCommonModule,
      MyndTooltipModule,
      MyndInputFooterComponent,
      MyndLabelActionComponent,
      MyndLabelComponent,
    ],
    declarations: [
      MyndTextInputComponent,
      MyndValidateControlDirective,
    ],
  });

  const resetSource = new EventEmitter();

  const setupDirective = (() => {
    spectator = createDirective(`
      <form [formGroup]="form" [mResetFormValidation]="resetSource">
        <m-text-input
          mValidateControl
          label="Default Validation"
          controlUpdateStrategy="onInput"
          formControlName="name">
        </m-text-input>

        <button type="submit">Submit</button>
        <button type="reset">Reset Form</button>
      </form>
    `, {
      hostProps: {
        resetSource: null,
        form: new UntypedFormBuilder().group({
          name: ['', Validators.required],
        }) as UntypedFormGroup,
      },
    });
  });

  beforeEach(() => {
    setupDirective();
  });

  it('should not have errors initially (when pristine)', () => {
    expect(spectator.directive['formDirective'].valid).toBeFalsy();
    expect(spectator.directive['formDirective'].touched).toBeFalsy();
    expect(spectator.query('m-form-input-footer')).not.toHaveExactText('This field is required');
  });

  it('should clear errors on reset form click if custom resetSource was not passed in', () => {
    spectator.click('[type="submit"]');

    expect(spectator.directive['formDirective'].directives[0].valueAccessor['error']).toBeTruthy();
    expect(spectator.query('m-form-input-footer')).toHaveExactText('This field is required');

    spectator.click('[type="reset"]');

    expect(spectator.directive['formDirective'].directives[0].valueAccessor['error']).toBeFalsy();
    expect(spectator.query('m-form-input-footer')).toHaveExactText('');
  });

  it('should clear errors on on custom resetSource emit', () => {
    spectator.click('button[type="submit"]');

    expect(spectator.directive['formDirective'].directives[0].valueAccessor['error']).toBeTruthy();
    expect(spectator.query('m-form-input-footer')).toHaveExactText('This field is required');

    spectator.setHostInput('resetSource', resetSource);
    resetSource.next(null);

    // spectator.detectChanges doesn't work here for some reason
    spectator.query(MyndValidateControlDirective)['cdr'].detectChanges();

    expect(spectator.directive['formDirective'].directives[0].valueAccessor['error']).toBeFalsy();
    expect(spectator.query('m-form-input-footer')).toHaveExactText('');
  });
});
