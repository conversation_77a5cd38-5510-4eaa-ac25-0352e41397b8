import { ReactiveFormsModule, UntypedFormBuilder, Validators } from '@angular/forms';
import { MyndCommonModule } from '@myndmanagement/common';
import { MyndTooltipModule } from '@myndmanagement/ui-tooltip';
import { SpectatorDirective, createDirectiveFactory } from '@ngneat/spectator/jest';
import { Subject } from 'rxjs';

import { MyndInputFooterComponent } from '../components/input-footer/input-footer.component';
import { MyndLabelComponent } from '../components/label/label.component';
import { MyndLabelActionComponent } from '../components/label-action/label-action.component';
import { MyndTextInputComponent } from '../components/text-input/text-input.component';

import { MyndMainFormDirective, MyndValidateControlDirective } from './validate-control.directive';

describe('MyndValidateControlDirective', () => {
  let spectator: SpectatorDirective<MyndValidateControlDirective>;
  const mockSubmitObservable = new Subject<void>();
  const mockMainFormDirective = {
    formDirective: {
      ngSubmit: mockSubmitObservable.asObservable(),
      submitted: true,
    },
  };
  const createDirective = createDirectiveFactory({
    directive: MyndValidateControlDirective,
    imports: [
      ReactiveFormsModule,
      MyndCommonModule,
      MyndTooltipModule,
      MyndLabelActionComponent,
      MyndLabelComponent,
      MyndInputFooterComponent,
    ],
    declarations: [MyndTextInputComponent],
    providers: [
      { provide: MyndMainFormDirective, useValue: mockMainFormDirective },
    ],
  });

  const setupDirective = (() => {
    spectator = createDirective(`
      <form [formGroup]="form">
        <m-text-input
          mValidateControl
          label="Default Validation"
          controlUpdateStrategy="onInput"
          formControlName="name">
        </m-text-input>

        <button m-button type="submit">Submit</button>
      </form>
    `, {
      hostProps: {
        form: new UntypedFormBuilder().group({
          name: ['', Validators.required],
        }),
      },
    });
  });

  it('should not have error initially (when pristine)', () => {
    setupDirective();

    expect(spectator.directive.error).toBeFalsy();
    expect(spectator.query('m-form-input-footer')).not.toHaveExactText('This field is required');
  });

  it('should have error on submit', () => {
    setupDirective();

    spectator.click('button');

    expect(spectator.directive.error).toBe('This field is required');
    expect(spectator.query('m-form-input-footer')).toHaveExactText('This field is required');
  });

  it('should have error on input blur if input is touched', () => {
    setupDirective();

    spectator.directive['onBlur'] = () => {
      spectator.directive['valueAccessor'].controlTouch.next();
    };
    spectator.directive['ngControl'].control.markAsTouched();
    spectator.directive['onBlur']();

    spectator.detectChanges();

    expect(spectator.directive.error).toBe('This field is required');
    expect(spectator.query('m-form-input-footer')).toHaveExactText('This field is required');
  });

  it('should not have error on input blur if input is touched but controlTouch is not provided', () => {
    setupDirective();

    spectator.directive['valueAccessor'].controlTouch = null;
    spectator.directive['onBlur'] = () => {
    };

    spectator.directive['ngControl'].control.markAsTouched();
    spectator.directive['onBlur']();

    spectator.detectChanges();

    expect(spectator.directive.error).not.toExist();
    expect(spectator.query('m-form-input-footer')).toHaveExactText('');
  });

  it('should have error state changes if input is touched', () => {
    setupDirective();

    spectator.directive['ngControl'].control.markAsTouched();
    spectator.directive['ngControl'].control.disable();
    spectator.directive['ngControl'].control.enable();

    spectator.detectChanges();

    expect(spectator.directive.error).toBe('This field is required');
    expect(spectator.query('m-form-input-footer')).toHaveExactText('This field is required');
  });

  it('should react to parent form submission when mValidateOnParentFormSubmit is true', () => {

    spectator = createDirective(
      `
      <form [formGroup]="form">
        <m-text-input
          mValidateControl [mValidateOnParentFormSubmit]="true"
          label="Default Validation"
          controlUpdateStrategy="onInput"
          formControlName="name">
        </m-text-input>

        <button m-button type="submit">Submit</button>
      </form>
    `,
      {
        hostProps: {
          form: new UntypedFormBuilder().group({
            name: ['', Validators.required],
          }),
        },
      },
    );

    // Simulate parent form submission
    mockSubmitObservable.next();

    spectator.detectChanges();

    expect(spectator.directive.error).toBe('This field is required');
    expect(spectator.query('m-form-input-footer')).toHaveExactText('This field is required');
  });

  it('ignore parent form submission when mValidateOnParentFormSubmit is false', () => {

    spectator = createDirective(`
    <form [formGroup]="form">
      <m-text-input
        mValidateControl [mValidateOnParentFormSubmit]="false"
        label="Default Validation"
        controlUpdateStrategy="onInput"
        formControlName="name">
      </m-text-input>

      <button m-button type="submit">Submit</button>
    </form>
  `, {
      hostProps: {
        form: new UntypedFormBuilder().group({
          name: ['', Validators.required],
        }),
      }});

    // Simulate parent form submission
    mockSubmitObservable.next();

    spectator.detectChanges();

    expect(spectator.directive.error).toBeFalsy();
  });

});
