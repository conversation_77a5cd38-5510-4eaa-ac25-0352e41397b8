import { ReactiveFormsModule, UntypedFormControl } from '@angular/forms';
import { MyndCommonModule } from '@myndmanagement/common';
import { SpectatorHost, createHostFactory } from '@ngneat/spectator/jest';

import { MyndInputFooterComponent } from '../input-footer/input-footer.component';
import { MyndLabelComponent } from '../label/label.component';
import { MyndLabelActionComponent } from '../label-action/label-action.component';

import { MyndCheckboxComponent } from './checkbox.component';

describe('MyndCheckboxComponent', () => {
  let spectator: SpectatorHost<MyndCheckboxComponent, { formControl: UntypedFormControl }>;
  const createHost = createHostFactory({
    component: MyndCheckboxComponent,
    imports: [
      MyndCommonModule,
      ReactiveFormsModule,
      MyndLabelComponent,
      MyndLabelActionComponent,
      MyndInputFooterComponent,
    ],
  });

  beforeEach(() => {
    spectator = createHost('<m-form-checkbox [formControl]="formControl"></m-form-checkbox>', {
      hostProps: {
        formControl: new UntypedFormControl(),
      },
    });
  });

  it('is false by default', () => {
    expect(spectator.query('.icon-check')).not.toExist();
  });

  it('should toggle true/false on click', () => {
    spectator.click();

    expect(spectator.query('.icon-check')).toExist();
    expect(spectator.hostComponent.formControl.value).toBe(true);

    spectator.click();

    expect(spectator.query('.icon-check')).not.toExist();
    expect(spectator.hostComponent.formControl.value).toBe(false);
  });

  it('should enable/disable on input change or set disabled state call', () => {
    spectator.setHostInput('formControl', new UntypedFormControl(false));
    spectator.component.label = 'Label';
    spectator.component.disabled = true;

    expect(spectator.component.value).toBe(false);

    spectator.click();
    expect(spectator.component.value).toBe(false);

    spectator.component.setDisabledState(false);
    spectator.click();
    expect(spectator.component.value).toBe(true);

    spectator.component.setDisabledState(true);
    spectator.click();
    expect(spectator.component.value).toBe(true);
  });
});
