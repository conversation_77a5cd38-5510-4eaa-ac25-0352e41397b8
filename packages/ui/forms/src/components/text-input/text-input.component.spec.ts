import { ReactiveFormsModule, UntypedFormControl } from '@angular/forms';
import { MyndCommonModule } from '@myndmanagement/common';
import { MyndTooltipModule } from '@myndmanagement/ui-tooltip';
import { SpectatorHost, byText, createHostFactory } from '@ngneat/spectator/jest';

import { MyndInputFooterComponent } from '../input-footer/input-footer.component';
import { MyndLabelComponent } from '../label/label.component';
import { MyndLabelActionComponent } from '../label-action/label-action.component';

import { MyndTextInputComponent } from './text-input.component';

describe(MyndTextInputComponent.name, () => {
  let spectator: SpectatorHost<MyndTextInputComponent, { formControl: UntypedFormControl }>;
  const createHost = createHostFactory({
    component: MyndTextInputComponent,
    imports: [
      MyndCommonModule,
      ReactiveFormsModule,
      MyndTooltipModule,
      MyndLabelComponent,
      MyndLabelActionComponent,
      MyndInputFooterComponent,
    ],
  });

  beforeEach(() => {
    spectator = createHost('<m-text-input [formControl]="formControl" [type]="type"></m-text-input>', {
      hostProps: {
        formControl: new UntypedFormControl(),
        type: 'text',
      },
    });
  });

  it('sets form control value when text is typed', () => {
    spectator.typeInElement('It works', 'input');
    spectator.dispatchFakeEvent('input', 'change');

    expect(spectator.hostComponent.formControl.value).toBe('It works');
  });

  it('clears value when Clear link is pressed', () => {
    spectator.setHostInput('formControl', new UntypedFormControl('Existing text'));
    spectator.component.isClearButtonVisible = true;
    spectator.component.label = 'Test input';
    spectator.detectComponentChanges();

    spectator.click(spectator.query(byText('Clear')));

    expect(spectator.hostComponent.formControl.value).toBe(null);
  });

  it('returns trimmed email with all lower case letters for email type', () => {
    spectator.setHostInput('type', 'email');

    spectator.typeInElement('<EMAIL>     ', 'input');
    spectator.dispatchFakeEvent('input', 'change');

    expect(spectator.hostComponent.formControl.value).toBe('<EMAIL>');
  });

  it('converts hours to minutes for duration type', () => {
    spectator.setHostInput('type', 'duration');

    spectator.typeInElement('10', 'input');
    spectator.dispatchFakeEvent('input', 'change');

    expect(spectator.hostComponent.formControl.value).toBe(600);
  });

  it('shows currency value with correct input type on input focus, apply correct control value, then shows currency value on input blur', () => {
    spectator.setHostInput('type', 'currency');

    spectator.typeInElement('100', 'input');
    spectator.dispatchFakeEvent('input', 'focus');

    expect(spectator.query('input')).toHaveAttribute('type', 'number');
    expect(spectator.query('input')).toHaveValue('100.00');

    spectator.dispatchFakeEvent('input', 'change');
    expect(spectator.hostComponent.formControl.value).toBe('100.00');

    spectator.component['focusedAt'] -= 300;
    spectator.dispatchFakeEvent('input', 'blur');
    expect(spectator.query('input')).toHaveValue('$100.00');
  });

  it('keeps input empty when form value is "null" and shows 0 when form value is 0', () => {
    spectator.setHostInput('type', 'currency');

    spectator.hostComponent.formControl.setValue(null);
    spectator.detectComponentChanges();
    expect(spectator.query('input')).toHaveValue('');

    spectator.hostComponent.formControl.setValue(0);
    spectator.detectComponentChanges();
    expect(spectator.query('input')).toHaveValue('$0.00');
  });

  it('shows percent value with correct input type on input focus, apply correct control value', () => {
    spectator.setHostInput('type', 'percent');

    spectator.typeInElement('50', 'input');
    spectator.dispatchFakeEvent('input', 'focus');

    expect(spectator.query('input')).toHaveAttribute('type', 'number');
    expect(spectator.query('input')).toHaveValue('50.00');

    spectator.dispatchFakeEvent('input', 'change');
    expect(spectator.hostComponent.formControl.value).toBe(50);

    spectator.component['focusedAt'] -= 300;
    spectator.dispatchFakeEvent('input', 'blur');
    expect(spectator.query('input')).toHaveValue('50.00%');
  });

  it('shows commas number value with correct input type on input focus, apply correct control value', () => {
    spectator.setHostInput('type', 'commasNumber');

    spectator.typeInElement('2,0,3', 'input');
    spectator.dispatchFakeEvent('input', 'focus');

    expect(spectator.query('input')).toHaveAttribute('type', 'number');
    expect(spectator.query('input')).toHaveValue('203');

    spectator.dispatchFakeEvent('input', 'change');
    expect(spectator.hostComponent.formControl.value).toBe(203);

    spectator.component['focusedAt'] -= 300;
    spectator.dispatchFakeEvent('input', 'blur');
    expect(spectator.query('input')).toHaveValue('203');
  });

  it('shows email value on input blur', () => {
    spectator.setHostInput('type', 'email');

    spectator.typeInElement('<EMAIL>', 'input');
    spectator.dispatchFakeEvent('input', 'change');
    spectator.component['focusedAt'] -= 300;
    spectator.dispatchFakeEvent('input', 'blur');

    expect(spectator.query('input')).toHaveValue('<EMAIL>');
  });

  describe('testing input with "number" type', () => {
    beforeEach(() => {
      spectator.setHostInput('type', 'number');
    });

    it('shows number value on input blur', () => {
      spectator.typeInElement('100', 'input');
      spectator.dispatchFakeEvent('input', 'change');
      spectator.component['focusedAt'] -= 300;
      spectator.dispatchFakeEvent('input', 'blur');

      expect(spectator.query('input')).toHaveValue('100');
    });

    it('shows "0" value when control value was setup programmatically', () => {
      spectator.hostComponent.formControl.patchValue(0);

      spectator.detectChanges();

      expect(spectator.query('input')).toHaveValue('0');
    });

    it('shows "0" number value on input blur', () => {
      spectator.typeInElement('0', 'input');
      spectator.dispatchFakeEvent('input', 'change');
      spectator.component['focusedAt'] -= 300;
      spectator.dispatchFakeEvent('input', 'blur');

      expect(spectator.query('input')).toHaveValue('0');
    });

    it('shows empty input on input blur', () => {
      spectator.typeInElement('', 'input');
      spectator.dispatchFakeEvent('input', 'change');
      spectator.component['focusedAt'] -= 300;
      spectator.dispatchFakeEvent('input', 'blur');

      expect(spectator.query('input')).toHaveValue('');
    });
  });

  it('shows phone value on input blur', () => {
    spectator.setHostInput('type', 'phone');

    spectator.typeInElement('1234567890', 'input');
    spectator.dispatchFakeEvent('input', 'change');
    spectator.component['focusedAt'] -= 300;
    spectator.dispatchFakeEvent('input', 'blur');

    expect(spectator.query('input')).toHaveValue('(*************');
  });

  it('updates phone form control value', () => {
    spectator.setHostInput('type', 'phone');

    spectator.component.controlUpdateStrategy = 'onInput';
    spectator.typeInElement('1234567890', 'input');

    expect(spectator.hostComponent.formControl.value).toBe('+11234567890');
  });

  it('shows duration value on input blur', () => {
    spectator.setHostInput('type', 'duration');

    spectator.typeInElement('100', 'input');
    spectator.dispatchFakeEvent('input', 'change');
    spectator.component['focusedAt'] -= 300;
    spectator.dispatchFakeEvent('input', 'blur');

    expect(spectator.query('input')).toHaveValue('1h 40m');
  });

  it('updates form control value according to passed in update strategy', () => {
    spectator.typeInElement('test', 'input');
    expect(spectator.hostComponent.formControl.value).toBe(null);

    spectator.dispatchFakeEvent('input', 'change');
    expect(spectator.hostComponent.formControl.value).toBe('test');

    spectator.component.controlUpdateStrategy = 'onInput';
    spectator.typeInElement('test 2', 'input');
    expect(spectator.hostComponent.formControl.value).toBe('test 2');

    spectator.component.controlUpdateStrategy = 'onChange';
    spectator.typeInElement('test 3', 'input');
    expect(spectator.hostComponent.formControl.value).toBe('test 2');

    spectator.dispatchFakeEvent('input', 'change');
    expect(spectator.hostComponent.formControl.value).toBe('test 3');
  });

  it('shows/hides value for password type on eye icon click', () => {
    spectator.setHostInput('type', 'password');

    spectator.typeInElement('some text', 'input');
    spectator.dispatchFakeEvent('input', 'change');

    expect(spectator.query('input')).toHaveAttribute('type', 'password');
    expect(spectator.query('input')).toHaveValue('some text');

    spectator.click('.icon-eye-md');

    expect(spectator.query('input')).toHaveAttribute('type', 'text');
    expect(spectator.query('input')).toHaveValue('some text');
  });

  it('enables/disables label and input on set disabled state call', () => {
    spectator.setHostInput('formControl', new UntypedFormControl('Some text'));
    spectator.component.label = 'Label';

    spectator.component.setDisabledState(false);
    spectator.detectComponentChanges();
    expect(spectator.query(MyndLabelComponent).isClearButtonDisabled).toBeFalsy();
    expect(spectator.query('input')).not.toHaveAttribute('disabled', 'disabled');

    spectator.component.setDisabledState(true);
    spectator.detectComponentChanges();
    expect(spectator.query(MyndLabelComponent).isClearButtonDisabled).toBeTruthy();
    expect(spectator.query('input')).toHaveAttribute('disabled', 'disabled');
  });

  describe('inline cleaner', () => {
    const cleanerSelector = '.icon-cancel-md';

    describe('native input controls', () => {
      const inputSelector = 'input';
      const hideControlsClass = 'hide-controls';

      it('should hide native input controls when hasInlineCleaner is true', () => {
        spectator.component.hasInlineCleaner = true;
        spectator.detectComponentChanges();

        expect(spectator.query(inputSelector)).toHaveClass(hideControlsClass);
      });

      it('shouldn\'t hide native input controls when hasInlineCleaner is false', () => {
        spectator.component.hasInlineCleaner = false;

        expect(spectator.query(inputSelector)).not.toHaveClass(hideControlsClass);
      });
    });

    describe('cleaner availability', () => {
      it('should show cleaner when hasInlineCleaner is true and value exists', () => {
        spectator.component.hasInlineCleaner = true;
        spectator.setHostInput({ formControl: new UntypedFormControl('value') });

        expect(spectator.query(cleanerSelector)).not.toBeNull();
      });

      it('shouldn\'t show cleaner when hasInlineCleaner is false', () => {
        spectator.component.hasInlineCleaner = false;
        spectator.setHostInput({ formControl: new UntypedFormControl('value') });

        expect(spectator.query(cleanerSelector)).toBeNull();
      });

      it('shouldn\'t show cleaner when there is no value', () => {
        spectator.component.hasInlineCleaner = true;
        spectator.setHostInput({ formControl: new UntypedFormControl() });

        expect(spectator.query(cleanerSelector)).toBeNull();
      });
    });

    it('should clear the value on click', () => {
      spectator.component.hasInlineCleaner = true;
      spectator.setHostInput({ formControl: new UntypedFormControl('value') });
      spectator.click(cleanerSelector);

      expect(spectator.hostComponent.formControl.value).toBe(null);
    });
  });
});
