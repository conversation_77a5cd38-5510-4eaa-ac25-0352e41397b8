import { ReactiveFormsModule, UntypedFormControl } from '@angular/forms';
import { MyndCommonModule } from '@myndmanagement/common';
import { SpectatorHost, byText, createHostFactory } from '@ngneat/spectator/jest';

import { MyndInputFooterComponent } from '../input-footer/input-footer.component';
import { MyndLabelComponent } from '../label/label.component';
import { MyndLabelActionComponent } from '../label-action/label-action.component';

import { MyndTimeInputComponent } from './time-input.component';

describe('MyndTimeInputComponent', () => {
  let spectator: SpectatorHost<MyndTimeInputComponent, { formControl: UntypedFormControl }>;
  const createHost = createHostFactory({
    component: MyndTimeInputComponent,
    imports: [
      MyndCommonModule,
      ReactiveFormsModule,
      MyndLabelComponent,
      MyndInputFooterComponent,
      MyndLabelActionComponent,
    ],
  });

  beforeEach(() => {
    spectator = createHost(`<m-time-input
      [formControl]="formControl"
      [label]="label"
      [showClear]="showClear"
    ></m-time-input>`, {
      hostProps: {
        formControl: new UntypedFormControl(),
      },
    });
  });

  it('updates form control value when time is set', () => {
    spectator.typeInElement('13:01', 'input');
    spectator.dispatchFakeEvent('input', 'change');

    expect(spectator.hostComponent.formControl.value).toBe('13:01');
  });

  it('clears value when Clear link is pressed', () => {
    spectator.setHostInput({
      formControl: new UntypedFormControl('13:02'),
      label: 'Time input',
      showClear: true,
    });

    spectator.click(spectator.query(byText('Clear')));

    expect(spectator.component.value).toBe(null);
  });

  it('should enable/disable label and input on set disabled state call', () => {
    spectator.setHostInput({
      formControl: new UntypedFormControl('13:02'),
      label: 'Label',
    });

    spectator.component.setDisabledState(false);
    spectator.detectComponentChanges();
    expect(spectator.query(MyndLabelComponent).isClearButtonDisabled).toBeFalsy();
    expect(spectator.query('input')).not.toHaveAttribute('disabled', 'disabled');

    spectator.component.setDisabledState(true);
    spectator.detectComponentChanges();
    expect(spectator.query(MyndLabelComponent).isClearButtonDisabled).toBeTruthy();
    expect(spectator.query('input')).toHaveAttribute('disabled', 'disabled');
  });
});
