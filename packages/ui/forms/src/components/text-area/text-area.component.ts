import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  EventEmitter,
  Input,
  OnInit,
  Output,
  forwardRef,
  input,
} from '@angular/core';
import { NG_VALUE_ACCESSOR } from '@angular/forms';

import { MyndTextareaAutoExpandDirective } from '../../directives/textarea-auto-expand.directive';
import { IMyndValidatableControlValueAccessor } from '../../directives/validate-control.directive';
import { generateUniqId } from '../../utils/generate-uniq-id.util';
import { MyndInputFooterComponent } from '../input-footer/input-footer.component';
import { MyndLabelComponent } from '../label/label.component';

@Component({
  selector: 'm-text-area',
  templateUrl: 'text-area.component.html',
  styleUrls: ['text-area.component.scss'],
  providers: [{
    provide: NG_VALUE_ACCESSOR,
    useExisting: forwardRef(() => MyndTextAreaComponent),
    multi: true,
  }],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    MyndTextareaAutoExpandDirective,
    MyndInputFooterComponent,
    MyndLabelComponent,
  ],
})
export class MyndTextAreaComponent implements IMyndValidatableControlValueAccessor, OnInit {
  @Input() showClear = false;

  /**
   * Adds autofocus attribute, which tells browser to put focus on the element.
   */
  @Input() autofocus = false;

  /**
   * Makes textarea to auto expand to fit input content
   */
  isAutoExpand = input<boolean>(false);

  @Input() label: string;
  @Input() error: string;
  @Input() disabled = false;
  @Input() isInTableCell: boolean;
  @Input() placeholder = '';

  /**
   * Render the `optional` label.
   */
  @Input() isOptional = false;

  /**
   * Number of rows to show.
   */
  rows = input<number, number>(2, { transform: v => v || 2 });

  /**
   * Value for maxlength attribute.
   */
  @Input() maxLength = 524288; // 524288 is default for textarea

  /**
   * Value for resize CSS property.
   * Defaults to `auto` which means that initial value is dependent on the browser.
   * Chrome for example allows textareas to be resized by default.
   */
  @Input() resize = 'auto';

  @Input() helpText: string;

  @Output() controlTouch = new EventEmitter<void>();

  value = '';
  inputId: string;

  private propagateChange: (value: string) => void;
  private propagateTouch: () => void;

  constructor(private cdr: ChangeDetectorRef) {}

  ngOnInit(): void {
    this.inputId = generateUniqId(this.label);
  }

  clear(): void {
    this.value = null;

    if (this.propagateChange) {
      this.propagateChange(this.value);
    }

    this.onTouch();
  }

  onTouch(): void {
    this.propagateTouch?.();
  }

  onChange(event: Event): void {
    this.value = (event.target as HTMLInputElement).value;

    if (this.propagateChange) {
      this.propagateChange(this.value);
    }

    this.onTouch();
  }

  registerOnChange(fn: (value: string) => void): void {
    this.propagateChange = fn;
  }

  registerOnTouched(fn: () => void): void {
    this.propagateTouch = () => {
      fn();
      this.controlTouch.emit();
    };
  }

  writeValue(value: string): void {
    this.value = value;
    this.cdr.markForCheck();
  }

  setDisabledState(isDisabled: boolean): void {
    this.disabled = isDisabled;
    this.cdr.markForCheck();
  }
}
