<div class="text-area" [class.error]="error" [class.in-table-cell]="isInTableCell">
  @if (label) {
    <div class="label-content">
      <div class="label-name">
        <m-label
          [for]="inputId"
          [isError]="error"
          [isClearButtonVisible]="showClear"
          [isClearButtonDisabled]="disabled"
          [isOptional]="isOptional"
          (clear)="clear()"
        >{{ label }}</m-label>
      </div>
    </div>
  }
  <textarea
    mTextareaAutoExpand
    [mTextareaAutoExpandEnabled]="isAutoExpand()"
    [attr.id]="inputId || null"
    [rows]="rows()"
    [maxLength]="maxLength"
    [attr.disabled]="disabled ? '' : null"
    [attr.autofocus]="autofocus ? '' : null"
    [class.in-table-cell]="isInTableCell"
    [style.resize]="resize"
    [value]="value"
    [placeholder]="placeholder"
    (change)="onChange($event)"
    (keyup)="onChange($event)"
    (blur)="onTouch()"
  ></textarea>
  <m-form-input-footer [helpText]="helpText" [error]="error" [isInTableCell]="isInTableCell"/>
</div>
