@use '@myndmanagement/styles/styles/blocks';
@use '@myndmanagement/styles/styles/variables';

@mixin invalidControlStyles {
  textarea {
    border-color: variables.$color-mynd-red;
  }
}

:host {
  &.ng-touched.ng-invalid {
    .text-area {
      ::ng-deep .label-text {
        color: variables.$color-mynd-red;
      }

      @include invalidControlStyles;
    }
  }
}

.text-area {
  @include blocks.formFieldWrapper;

  position: relative;

  &.in-table-cell {
    margin: 0;

    textarea {
      @include blocks.formFieldSmallInTableCell;
    }

    &.error {
      textarea {
        @include blocks.formFieldSmallInTableCellError;
      }
    }
  }

  &.error {
    @include invalidControlStyles;
  }

  .help-text {
    font-size: 12px;
  }

  & textarea:disabled {
    color: variables.$color-mynd-navy-60;
    background: variables.$color-mynd-navy-5;
    border-color: variables.$color-mynd-navy-15;
  }

  &.read-only {
    textarea {
      border: 0;
      padding: 0;
      resize: none !important;

      &:disabled {
        background-color: transparent;
      }
    }
  }

  textarea {
    @include blocks.formFieldBase;

    width: 100%;
    padding: 8px 12px;
  }
}
