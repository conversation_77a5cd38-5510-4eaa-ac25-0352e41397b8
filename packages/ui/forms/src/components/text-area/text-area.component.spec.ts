import { fakeAsync } from '@angular/core/testing';
import { ReactiveFormsModule, UntypedFormControl } from '@angular/forms';
import { MyndTextareaAutoExpandDirective } from '@myndmanagement/ui-forms';
import { SpectatorHost, createHostFactory } from '@ngneat/spectator/jest';
import { MockComponent, MockDirective } from 'ng-mocks';

import { MyndInputFooterComponent } from '../input-footer/input-footer.component';
import { MyndLabelComponent } from '../label/label.component';

import { MyndTextAreaComponent } from './text-area.component';

describe('MyndTextAreaComponent', () => {
  let spectator: SpectatorHost<MyndTextAreaComponent, { formControl: UntypedFormControl }>;
  const createHost = createHostFactory({
    component: MyndTextAreaComponent,
    imports: [
      ReactiveFormsModule,
      MockDirective(MyndTextareaAutoExpandDirective),
      MockComponent(MyndInputFooterComponent),
      MockComponent(MyndLabelComponent),
    ],
  });

  beforeEach(() => {
    spectator = createHost(`<m-text-area
      [rows]="2"
      [disabled]="disabled"
      [formControl]="formControl"
      [isAutoExpand]="isAutoExpand"
      [label]="label"
      [showClear]="showClear"
    ></m-text-area>`, {
      hostProps: {
        formControl: new UntypedFormControl(),
      },
    });
  });

  it('updates form control value when text is typed', () => {
    spectator.typeInElement('My text', 'textarea');
    spectator.dispatchFakeEvent('textarea', 'change');

    expect(spectator.hostComponent.formControl.value).toBe('My text');
  });

  it('has directive if auto expand is set as true', fakeAsync(() => {
    spectator.setHostInput('isAutoExpand', true);
    expect(spectator.query(MyndTextareaAutoExpandDirective).mTextareaAutoExpandEnabled).toBe(true);
  }));

  it('clears value when Clear link is pressed', () => {
    spectator.setHostInput({
      formControl: new UntypedFormControl('Existing text'),
      label: 'My test textarea',
      showClear: true,
    });

    spectator.dispatchFakeEvent('m-label', 'clear');

    expect(spectator.component.value).toBe(null);
  });

  it('should disable label and textarea', () => {
    spectator.setHostInput({
      formControl: new UntypedFormControl('Text'),
      label: 'Label',
    });
    spectator.setHostInput('disabled', true);

    expect(spectator.query(MyndLabelComponent).isClearButtonDisabled).toBeTruthy();
    expect(spectator.query('textarea')).toHaveAttribute('disabled', 'disabled');
  });

  it('should enable/disable label and textarea on set disabled state call', () => {
    spectator.setHostInput({
      formControl: new UntypedFormControl('Text'),
      label: 'Label',
    });

    spectator.component.setDisabledState(false);
    spectator.detectComponentChanges();
    expect(spectator.query(MyndLabelComponent).isClearButtonDisabled).toBeFalsy();
    expect(spectator.query('textarea')).not.toHaveAttribute('disabled', 'disabled');

    spectator.component.setDisabledState(true);
    spectator.detectComponentChanges();
    expect(spectator.query(MyndLabelComponent).isClearButtonDisabled).toBeTruthy();
    expect(spectator.query('textarea')).toHaveAttribute('disabled', 'disabled');
  });
});
