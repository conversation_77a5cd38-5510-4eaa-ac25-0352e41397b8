import { ReactiveFormsModule, UntypedFormControl } from '@angular/forms';
import { MyndCommonModule } from '@myndmanagement/common';
import { SpectatorHost, byText, createHostFactory } from '@ngneat/spectator/jest';

import { MyndInputFooterComponent } from '../input-footer/input-footer.component';
import { MyndLabelComponent } from '../label/label.component';
import { MyndLabelActionComponent } from '../label-action/label-action.component';

import { MyndDateInputComponent } from './date-input.component';

describe('MyndDateInputComponent', () => {
  let spectator: SpectatorHost<MyndDateInputComponent, { formControl: UntypedFormControl }>;
  const createHost = createHostFactory({
    component: MyndDateInputComponent,
    imports: [
      MyndCommonModule,
      ReactiveFormsModule,
      MyndInputFooterComponent,
      MyndLabelComponent,
      MyndLabelActionComponent,
    ],
  });

  beforeEach(() => {
    spectator = createHost('<m-date-input [formControl]="formControl"></m-date-input>', {
      hostProps: {
        formControl: new UntypedFormControl(),
      },
    });
  });

  it('sets form control value when date is entered', () => {
    spectator.typeInElement('2019-09-07', 'input');
    spectator.dispatchFakeEvent('input', 'change');

    expect(spectator.hostComponent.formControl.value).toBe('2019-09-07');
  });

  it('clears value when Clear link is pressed', () => {
    spectator.setHostInput('formControl', new UntypedFormControl('2019-09-07'));
    spectator.component.showClear = true;
    spectator.component.label = 'Test input';
    spectator.detectComponentChanges();

    spectator.click(spectator.query(byText('Clear')));

    expect(spectator.hostComponent.formControl.value).toBe(null);
  });

  it('should disable label and input', () => {
    spectator.setHostInput('formControl', new UntypedFormControl('2019-09-07'));
    spectator.component.disabled = true;
    spectator.component.label = 'Label';
    spectator.detectComponentChanges();

    expect(spectator.query(MyndLabelComponent).isClearButtonDisabled).toBe(true);
    expect(spectator.query('input')).toHaveAttribute('disabled', 'disabled');
  });

  it('should enable/disable label and input on set disabled state call', () => {
    spectator.setHostInput('formControl', new UntypedFormControl('2019-09-07'));
    spectator.component.label = 'Label';

    spectator.component.setDisabledState(false);
    spectator.detectComponentChanges();
    expect(spectator.query(MyndLabelComponent).isClearButtonDisabled).toBeFalsy();
    expect(spectator.query('input')).not.toHaveAttribute('disabled', 'disabled');

    spectator.component.setDisabledState(true);
    spectator.detectComponentChanges();
    expect(spectator.query(MyndLabelComponent).isClearButtonDisabled).toBeTruthy();
    expect(spectator.query('input')).toHaveAttribute('disabled', 'disabled');
  });
});
