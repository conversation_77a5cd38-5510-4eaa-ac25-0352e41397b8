import { ReactiveFormsModule, UntypedFormControl } from '@angular/forms';
import { MyndCommonModule } from '@myndmanagement/common';
import { SpectatorHost, byText, createHostFactory } from '@ngneat/spectator/jest';

import { MyndInputFooterComponent } from '../input-footer/input-footer.component';
import { MyndLabelComponent } from '../label/label.component';
import { MyndLabelActionComponent } from '../label-action/label-action.component';
import { MyndTabbedOptionComponent } from '../tabbed-option/tabbed-option.component';

import { MyndTabbedOptionGroupComponent } from './tabbed-option-group.component';

const items: string[] = ['John', 'Mark', 'Martha'];

describe('MyndTabbedOptionGroupComponent', () => {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  let spectator: SpectatorHost<MyndTabbedOptionGroupComponent<any>, { formControl: UntypedFormControl; items: any[] }>;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const createHost = createHostFactory<MyndTabbedOptionGroupComponent<any>>({
    component: MyndTabbedOptionGroupComponent,
    imports: [
      ReactiveFormsModule,
      MyndCommonModule,
      MyndLabelComponent,
      MyndLabelActionComponent,
      MyndInputFooterComponent,
    ],
    declarations: [
      MyndTabbedOptionComponent,
    ],
  });

  beforeEach(() => {
    spectator = createHost(`
      <m-form-tabbed-option-group
        label="Using with simple items"
        [formControl]="formControl"
        [isClearButtonVisible]="isClearButtonVisible"
        [itemDisplayName]="itemDisplayName"
        [items]="items"
      ></m-form-tabbed-option-group>
    `, {
      hostProps: {
        items,
        formControl: new UntypedFormControl(),
      },
    });
  });

  it('renders an options list', () => {
    expect(spectator.queryAll('.tabbed-option')).toHaveLength(items.length);
    expect(spectator.queryAll('.tabbed-option')[0]).toHaveExactText(items[0]);
    expect(spectator.queryAll('.tabbed-option')[1]).toHaveExactText(items[1]);
    expect(spectator.queryAll('.tabbed-option')[2]).toHaveExactText(items[2]);
  });

  it('renders a label', () => {
    expect(spectator.query('m-label')).toHaveExactText('Using with simple items');
  });

  it('renders an options list based on objects options and itemDisplayName', () => {
    const objectItems = [
      { label: 'Label 1', value: 'Value 1' },
      { label: 'Label 2', value: 'Value 2' },
    ];
    const itemDisplayName = 'label';

    spectator.setHostInput({ itemDisplayName, items: objectItems });
    spectator.click('m-form-tabbed-option');

    expect(spectator.queryAll('.tabbed-option.selected')).toHaveExactText(objectItems[0][itemDisplayName]);
    expect(spectator.hostComponent.formControl.value).toBe(objectItems[0]);
  });

  it('allows user to press Clear button to unselect all options', () => {
    spectator.setHostInput('isClearButtonVisible', true);
    spectator.hostComponent.formControl.setValue(items[1]);
    spectator.detectChanges();
    expect(spectator.queryAll('.tabbed-option.selected')).toHaveLength(1);

    spectator.click(spectator.query(byText('Clear')));

    expect(spectator.queryAll('.tabbed-option.selected')).toHaveLength(0);
    expect(spectator.hostComponent.formControl.value).toEqual(null);
  });

  it('emits selected value on change and touch events', () => {
    const onChange = jest.fn();
    const onTouch = jest.fn();

    spectator.component.registerOnChange(onChange);
    spectator.component.registerOnTouched(onTouch);
    spectator.click('m-form-tabbed-option');

    expect(onChange).toHaveBeenCalledWith(items[0]);
    expect(onChange).toHaveBeenCalledTimes(1);
    expect(onTouch).toHaveBeenCalledTimes(1);
    expect(spectator.component.value).toBe(items[0]);
  });
});
