import { A11yModule } from '@angular/cdk/a11y';
import { OverlayModule } from '@angular/cdk/overlay';
import { fakeAsync, flush } from '@angular/core/testing';
import { FormsModule, ReactiveFormsModule, UntypedFormControl } from '@angular/forms';
import { MyndCommonModule } from '@myndmanagement/common';
import { SpectatorHost, createHostFactory } from '@ngneat/spectator/jest';

import { MyndInputFooterComponent } from '../input-footer/input-footer.component';
import { MyndLabelComponent } from '../label/label.component';
import { MyndLabelActionComponent } from '../label-action/label-action.component';
import { MyndOptionComponent } from '../option/option.component';
import { MyndOptionsContainerComponent } from '../options-container/options-container.component';
import { MyndRadioButtonComponent } from '../radio-button/radio-button.component';
import { MyndSelectComponent } from '../select/select.component';

import { MyndTimePickerComponent } from './time-picker.component';

describe('MyndTimePickerComponent', () => {
  let spectator: SpectatorHost<MyndTimePickerComponent, { formControl: UntypedFormControl }>;

  const createHost = createHostFactory({
    component: MyndTimePickerComponent,
    imports: [
      MyndCommonModule,
      OverlayModule,
      A11yModule,
      ReactiveFormsModule,
      FormsModule,
      MyndLabelComponent,
      MyndLabelActionComponent,
      MyndInputFooterComponent,
    ],
    declarations: [
      MyndOptionComponent,
      MyndSelectComponent,
      MyndRadioButtonComponent,
      MyndOptionsContainerComponent,
    ],
  });

  beforeEach(fakeAsync(() => {
    spectator = createHost('<m-time-picker [formControl]="formControl"></m-time-picker>', {
      hostProps: {
        formControl: new UntypedFormControl(),
      },
    });
    flush();
  }));

  it('renders a list of options from 05:00 to 20:00 with 15 minute step', () => {
    spectator.click('m-select .selected-value');
    const options = spectator.queryAll('m-option', { root: true });

    expect(options).toHaveLength(61);
    expect(options[0]).toHaveExactText('5:00am');
    expect(options[1]).toHaveExactText('5:15am');
    expect(options[10]).toHaveExactText('7:30am');
    expect(options[60]).toHaveExactText('8:00pm');
  });

  it('sets formControl value when user selects a value', fakeAsync(() => {
    spectator.click('m-select .selected-value');
    spectator.pickInSelect('8:30am', 'm-select');

    expect(spectator.hostComponent.formControl.value).toEqual({ hours: 8, minutes: 30, seconds: 0 });
  }));
});
