import { A11yModule } from '@angular/cdk/a11y';
import { OverlayModule } from '@angular/cdk/overlay';
import { fakeAsync, flush } from '@angular/core/testing';
import { ReactiveFormsModule, UntypedFormControl } from '@angular/forms';
import { MyndCommonModule } from '@myndmanagement/common';
import { SpectatorHost, createHostFactory } from '@ngneat/spectator/jest';

import { MyndInputFooterComponent } from '../input-footer/input-footer.component';
import { MyndLabelComponent } from '../label/label.component';
import { MyndLabelActionComponent } from '../label-action/label-action.component';
import { MyndOptionComponent } from '../option/option.component';
import { MyndOptionsContainerComponent } from '../options-container/options-container.component';
import { MyndRadioButtonComponent } from '../radio-button/radio-button.component';
import { MyndSelectComponent } from '../select/select.component';

import { MyndTimeSlotsDropdownComponent } from './time-slots-dropdown.component';

describe('MyndTimeSlotsDropdownComponent', () => {
  let spectator: SpectatorHost<MyndTimeSlotsDropdownComponent, { formControl: UntypedFormControl }>;

  const createHost = createHostFactory({
    component: MyndTimeSlotsDropdownComponent,
    imports: [
      MyndCommonModule,
      OverlayModule,
      A11yModule,
      ReactiveFormsModule,
      MyndLabelComponent,
      MyndLabelActionComponent,
      MyndInputFooterComponent,
    ],
    declarations: [
      MyndOptionComponent,
      MyndSelectComponent,
      MyndOptionsContainerComponent,
      MyndRadioButtonComponent,
    ],
  });

  beforeEach(fakeAsync(() => {
    spectator = createHost('<m-time-slots-dropdown [formControl]="formControl"></m-time-slots-dropdown>', {
      hostProps: {
        formControl: new UntypedFormControl(),
      },
    });
    flush();
  }));

  it('renders a list of hourly options between 08:00 and 20:00', () => {
    spectator.click('m-select .selected-value');

    const options = spectator.queryAll('m-option', { root: true });

    expect(options).toHaveLength(12);
    expect(options[0]).toHaveExactText('8:00am-9:00am');
    expect(options[1]).toHaveExactText('9:00am-10:00am');
    expect(options[3]).toHaveExactText('11:00am-12:00pm');
    expect(options[11]).toHaveExactText('7:00pm-8:00pm');
  });

  it('sets formControl value when user selects a value, clears value', fakeAsync(() => {
    spectator.click('m-select .selected-value');
    spectator.pickInSelect('9:00am-10:00am', 'm-select');

    expect(spectator.hostComponent.formControl.value).toEqual(['09:00', '10:00']);
  }));

  it('allows some intervals to be inactive', () => {
    spectator.component.externalIntervals = ['12:00', '14:00'];
    spectator.click('m-select .selected-value');

    const inactiveOptions = spectator.queryAll('m-option .inactive', { root: true });
    expect(inactiveOptions[0]).toHaveExactText('12:00pm-1:00pm');
    expect(inactiveOptions[1]).toHaveExactText('2:00pm-3:00pm');
  });

  it('enables/disables select on set disabled state call', () => {
    spectator.component.setDisabledState(true);
    spectator.detectComponentChanges();
    expect(spectator.query(MyndSelectComponent).disabled).toBe(true);

    spectator.click('m-select .selected-value');
    expect(spectator.queryAll('m-option .option')).toHaveLength(0);

    spectator.component.setDisabledState(false);
    spectator.detectComponentChanges();
    expect(spectator.query(MyndSelectComponent).disabled).toBe(false);

    spectator.click('m-select .selected-value');
    expect(spectator.queryAll('m-option .option')).toHaveLength(12);
  });
});
