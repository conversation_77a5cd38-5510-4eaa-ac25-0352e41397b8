import { A11yModule } from '@angular/cdk/a11y';
import { OverlayModule } from '@angular/cdk/overlay';
import { fakeAsync, tick } from '@angular/core/testing';
import { ReactiveFormsModule, UntypedFormControl } from '@angular/forms';
import { MyndCommonModule } from '@myndmanagement/common';
import { SpectatorHost, byText, createHostFactory } from '@ngneat/spectator/jest';

import { MyndInputFooterComponent } from '../input-footer/input-footer.component';
import { MyndLabelComponent } from '../label/label.component';
import { MyndLabelActionComponent } from '../label-action/label-action.component';
import { MyndOptionComponent } from '../option/option.component';
import { MyndOptionsContainerComponent } from '../options-container/options-container.component';
import { MyndRadioButtonComponent } from '../radio-button/radio-button.component';

import { InputPlacement, MyndTagsComponent } from './tags.component';

interface ITag {
  name: string;
}

describe('MyndTagsComponent', () => {
  let spectator: SpectatorHost<MyndTagsComponent<ITag>, { formControl: UntypedFormControl }>;
  const createHost = createHostFactory<MyndTagsComponent<ITag>>({
    component: MyndTagsComponent,
    detectChanges: false,
    imports: [
      MyndCommonModule,
      ReactiveFormsModule,
      OverlayModule,
      A11yModule,
      MyndLabelComponent,
      MyndLabelActionComponent,
      MyndInputFooterComponent,
    ],
    declarations: [
      MyndOptionComponent,
      MyndOptionsContainerComponent,
      MyndRadioButtonComponent,
    ],
  });

  beforeEach(() => {
    spectator = createHost(`<m-form-tags
      [formControl]="formControl"
      [inputPlacement]="inputPlacement"
      [itemDisplayName]="itemDisplayName"
      [items]="items"
    ></m-form-tags>`, {
      hostProps: {
        formControl: new UntypedFormControl([]),
        items: [
          {
            name: 'Deprecated',
          },
          {
            name: 'Buggy',
          },
        ],
        itemDisplayName: 'name',
        inputPlacement: InputPlacement.END,
      },
    });
  });

  it('sets form control value when tags are selected', () => {
    spectator.detectChanges();
    spectator.component.ngAfterViewInit();
    spectator.dispatchMouseEvent('input', 'mousedown');
    spectator.click(spectator.query(byText('Deprecated'), { root: true }));
    spectator.dispatchMouseEvent('input', 'mousedown');
    spectator.click(spectator.query(byText('Buggy'), { root: true }));

    expect(spectator.hostComponent.formControl.value).toEqual(['Deprecated', 'Buggy']);
  });

  it('shows selected form control tags', () => {
    spectator.detectChanges();
    spectator.setHostInput('formControl', new UntypedFormControl(['Existing', 'Another']));

    const existingTags = spectator.queryAll('.tags .tag');
    expect(existingTags[0]).toHaveExactText('Existing');
    expect(existingTags[1]).toHaveExactText('Another');
  });

  it('removes a tag when x button is pressed', () => {
    spectator.detectChanges();
    spectator.setHostInput('formControl', new UntypedFormControl(['Existing', 'NotYou', 'Another']));

    const xIcon = spectator.query(byText('NotYou')).parentNode.querySelector('.icon-cancel');
    spectator.click(xIcon);

    expect(spectator.hostComponent.formControl.value).toEqual(['Existing', 'Another']);
  });

  it('allows to add new tags that are not on the list', () => {
    spectator.detectChanges();
    for (const c of '+$NEW~TAG=^@') {
      spectator.dispatchKeyboardEvent('input', 'keypress', c);
    }
    spectator.dispatchKeyboardEvent('input', 'keypress', 'Enter');
    expect(spectator.hostComponent.formControl.value).toEqual(['newtag']);
  });

  it('hides options on click out of component', () => {
    spectator.component.searchStrategy = () => () => true;
    spectator.detectChanges();
    spectator.dispatchMouseEvent('input', 'mousedown');

    spectator.click(spectator.query('.cdk-overlay-backdrop', { root: true }));

    expect(spectator.component['isOptionsVisible']).toBeFalsy();
    expect(spectator.component.visibleItems).toEqual([]);
  });

  it('clears input value when Clear button is pressed', () => {
    spectator.component.showClear = true;
    spectator.component.label = 'Label';
    spectator.detectChanges();
    spectator.typeInElement('NewTag', 'input');

    spectator.click(spectator.query(byText('Clear')));

    expect(spectator.component.valueModel).toBeFalsy();
    expect(spectator.component.searchText).toBe('');
  });

  it('enables/disables label and input on set disabled state call', () => {
    spectator.detectChanges();
    spectator.setHostInput('formControl', new UntypedFormControl('555555555'));
    spectator.component.label = 'Label';

    spectator.component.setDisabledState(false);
    spectator.detectComponentChanges();
    expect(spectator.query(MyndLabelComponent).isClearButtonDisabled).toBeFalsy();
    expect(spectator.query('input')).not.toHaveAttribute('disabled', 'disabled');

    spectator.component.setDisabledState(true);
    spectator.detectComponentChanges();
    expect(spectator.query(MyndLabelComponent).isClearButtonDisabled).toBeTruthy();
    expect(spectator.query('input')).toHaveAttribute('disabled', 'disabled');
  });

  describe('searchOnInit = false', () => {
    beforeEach(() => {
      spectator.component.searchOnInit = false;
      spectator.detectComponentChanges();
    });

    it('does not show options by default', () => {
      spectator.detectChanges();
      expect(spectator.component.visibleItems).not.toBeDefined();
    });

    it('shows found options after type a tag name', fakeAsync(() => {
      spectator.detectChanges();
      spectator.dispatchMouseEvent('input', 'mousedown');
      spectator.typeInElement('deprec', 'input'); // search for the "Deprecated" value
      tick(200);
      spectator.detectChanges();

      const allOptions = spectator.queryAll('m-option', { root: true });
      expect(allOptions.length).toBe(2); // first m-option is with "Create" link
      expect(allOptions[1].textContent).toBe('Deprecated');
    }));
  });
});
