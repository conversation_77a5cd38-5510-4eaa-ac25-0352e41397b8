{"name": "ui-configured-table", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "packages/ui/configured-table/src", "prefix": "lib", "projectType": "library", "tags": [], "targets": {"test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "packages/ui/configured-table/jest.config.ts"}}, "lint": {"executor": "@nx/eslint:lint"}}}