{"name": "work-order-line-items", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "packages/work-order-line-items/src", "prefix": "lib", "projectType": "library", "tags": [], "targets": {"test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "packages/work-order-line-items/jest.config.ts"}}, "lint": {"executor": "@nx/eslint:lint"}}}