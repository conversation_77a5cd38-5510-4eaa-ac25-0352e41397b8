{"name": "accounting-recurring-invoice", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "packages/accounting/recurring-invoice/src", "prefix": "lib", "projectType": "library", "tags": [], "targets": {"test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "packages/accounting/recurring-invoice/jest.config.ts"}}, "lint": {"executor": "@nx/eslint:lint"}}}