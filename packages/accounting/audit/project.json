{"name": "accounting-audit", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "packages/accounting/audit/src", "prefix": "lib", "projectType": "library", "tags": [], "targets": {"test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "packages/accounting/audit/jest.config.ts"}}, "lint": {"executor": "@nx/eslint:lint"}}}