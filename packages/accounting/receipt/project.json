{"name": "accounting-receipt", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "packages/accounting/receipt/src", "prefix": "lib", "projectType": "library", "tags": [], "targets": {"test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "packages/accounting/receipt/jest.config.ts"}}, "lint": {"executor": "@nx/eslint:lint"}}}