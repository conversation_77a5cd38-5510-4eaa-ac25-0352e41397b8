{"name": "autocomplete-address", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "packages/autocomplete-address/src", "prefix": "lib", "projectType": "library", "tags": [], "targets": {"test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "packages/autocomplete-address/jest.config.ts"}}, "lint": {"executor": "@nx/eslint:lint"}}}