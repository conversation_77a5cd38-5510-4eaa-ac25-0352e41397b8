{"name": "external-chats", "$schema": "../../node_modules/nx/schemas/project-schema.json", "projectType": "library", "sourceRoot": "packages/external-chats/src", "targets": {"test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "packages/external-chats/jest.config.ts"}}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"]}}}