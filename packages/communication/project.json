{"name": "communication", "$schema": "../../node_modules/nx/schemas/project-schema.json", "projectType": "library", "sourceRoot": "packages/communication/src", "targets": {"test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "packages/communication/jest.config.ts"}}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"]}, "stylelint": {"executor": "nx-stylelint:lint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["{projectRoot}/**/*.css", "{projectRoot}/**/*.scss"]}}, "storybook": {"executor": "@storybook/angular:start-storybook", "options": {"port": 4400, "configDir": "packages/communication/.storybook", "browserTarget": "communication:build-storybook", "compodoc": false, "styles": ["node_modules/@angular/cdk/overlay-prebuilt.css", "node_modules/@myndmanagement/icon-font/font.css", "node_modules/quill/dist/quill.snow.css"]}, "configurations": {"ci": {"quiet": true}}}, "build-storybook": {"executor": "@storybook/angular:build-storybook", "outputs": ["{options.outputDir}"], "options": {"outputDir": "dist/storybook/communication", "configDir": "packages/communication/.storybook", "browserTarget": "communication:build-storybook", "compodoc": false}, "configurations": {"ci": {"quiet": true}}}}}