import { libraryGenerator } from '@nx/angular/generators';
import { generateFiles, joinPathFragments, names, offsetFromRoot, Tree } from '@nx/devkit';
import {
  determineProjectNameAndRootOptions,
  ProjectNameAndRootFormat,
} from '@nx/devkit/src/generators/project-name-and-root-utils';
import { getRootTsConfigFileName } from '@nx/js';
import * as path from 'path';

import { AngularLibraryGeneratorSchema } from './schema';

export async function angularLibraryGenerator(tree: Tree, options: AngularLibraryGeneratorSchema) {
  const libraryConfig: { name: string; directory: string; projectNameAndRootFormat: ProjectNameAndRootFormat } = {
    name: names(options.name).fileName,
    directory: options.directory,
    projectNameAndRootFormat: 'as-provided',
  };

  const { projectName, projectRoot } = await determineProjectNameAndRootOptions(tree, {
    ...libraryConfig,
    projectType: 'library',
    projectNameAndRootFormat: 'as-provided',
    callingGenerator: '@nx/angular:library',
  });

  await libraryGenerator(tree, {
    ...libraryConfig,
    buildable: false,
    simpleName: true,
    importPath: `@myndmanagement/${projectName}`,
  });

  tree.delete(`${projectRoot}/src/lib`);
  tree.delete(`${projectRoot}/src/test-setup.ts`);

  const rootOffset = offsetFromRoot(projectRoot);

  generateFiles(tree, path.join(__dirname, 'files'), projectRoot, {
    rootOffset,
    projectRoot,
    rootTsConfig: joinPathFragments(rootOffset, getRootTsConfigFileName(tree)),
  });
}

export default angularLibraryGenerator;
