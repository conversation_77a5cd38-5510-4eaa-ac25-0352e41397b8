import { Tree, formatFiles, getProjects, readJson, writeJson, updateProjectConfiguration } from '@nx/devkit';

export default async function convertBuildableToNonBuildable(tree: Tree) {
  const projects = getProjects(tree);
  let changed = 0;

  for (const [name, config] of projects) {
    if (config.projectType !== 'library') continue;

    const buildTarget = config.targets?.['build'];
    if (buildTarget?.executor !== '@nx/angular:ng-packagr-lite') continue;

    changed++;

    // 🧹 Remove "build" target
    delete config.targets?.['build'];
    updateProjectConfiguration(tree, name, config);

    const projectRoot = config.root;

    // 🗑️ Remove ng-package.json
    const ngPackagePath = `${projectRoot}/ng-package.json`;
    if (tree.exists(ngPackagePath)) {
      tree.delete(ngPackagePath);
      console.info(`🗑️ Removed ${ngPackagePath}`);
    }

    // 🗑️ Remove tsconfig.lib.prod.json
    const tsconfigProdPath = `${projectRoot}/tsconfig.lib.prod.json`;
    if (tree.exists(tsconfigProdPath)) {
      tree.delete(tsconfigProdPath);
      console.info(`🗑️ Removed ${tsconfigProdPath}`);
    }

    // 🧹 Remove reference to tsconfig.lib.prod.json in tsconfig.json
    const tsconfigPath = `${projectRoot}/tsconfig.json`;
    if (tree.exists(tsconfigPath)) {
      try {
        const tsconfig = readJson(tree, tsconfigPath);
        if (Array.isArray(tsconfig.references)) {
          const before = tsconfig.references.length;
          tsconfig.references = tsconfig.references.filter((r) => !r.path.includes('tsconfig.lib.prod.json'));
          if (before !== tsconfig.references.length) {
            writeJson(tree, tsconfigPath, tsconfig);
            console.info(`🧹 Removed reference from ${tsconfigPath} (${before - tsconfig.references.length} deleted)`);
          }
        }
      } catch (e) {
        console.warn(`⚠️ Skipped ${tsconfigPath}: invalid JSON`);
      }
    }

    console.info(`✅ Converted ${name} (${projectRoot}) to non-buildable`);
  }

  console.info(`\n🎉 Done! Converted ${changed} buildable libraries.\n`);

  await formatFiles(tree);
}
