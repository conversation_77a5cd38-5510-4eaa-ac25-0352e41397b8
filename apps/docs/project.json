{"name": "docs", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/docs/src", "projectType": "application", "prefix": "app", "tags": [], "targets": {"build": {"executor": "@ng-doc/builder:application", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/apps/docs", "index": "apps/docs/src/index.html", "browser": "apps/docs/src/main.ts", "polyfills": ["zone.js"], "tsConfig": "apps/docs/tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": [{"glob": "**/*", "input": "apps/docs/public"}, {"glob": "**/*", "input": "node_modules/@ng-doc/ui-kit/assets", "output": "assets/ng-doc/ui-kit"}, {"glob": "**/*", "input": "node_modules/@ng-doc/app/assets", "output": "assets/ng-doc/app"}, {"glob": "**/*", "input": "ng-doc/docs/assets", "output": "assets/ng-doc"}, {"glob": "*", "input": "packages/styles/assets/websites", "output": "."}, {"glob": "*", "input": "packages/styles/assets/images", "output": "assets/images"}], "stylePreprocessorOptions": {"sass": {"silenceDeprecations": ["mixed-decls", "color-functions", "global-builtin", "import"]}}, "styles": ["apps/docs/src/styles.scss", "dist/packages/icon-font/font.css", "node_modules/@ng-doc/app/styles/global.css"], "scripts": []}, "configurations": {"production": {"outputHashing": "all"}, "development": {"optimization": false, "extractLicenses": false, "sourceMap": true}}, "defaultConfiguration": "production"}, "serve": {"executor": "@ng-doc/builder:dev-server", "configurations": {"production": {"buildTarget": "docs:build:production", "port": 4215}, "development": {"buildTarget": "docs:build:development", "port": 4215}}, "defaultConfiguration": "development", "dependsOn": ["icon-font:build"]}, "extract-i18n": {"executor": "@angular-devkit/build-angular:extract-i18n", "options": {"buildTarget": "docs:build"}}, "lint": {"executor": "@nx/eslint:lint"}, "stylelint": {"executor": "nx-stylelint:lint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["{projectRoot}/**/*.css", "{projectRoot}/**/*.scss"]}}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "apps/docs/jest.config.ts"}}}, "implicitDependencies": ["icon-font", "devkit"]}