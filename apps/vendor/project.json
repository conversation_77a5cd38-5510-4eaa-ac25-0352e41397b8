{"name": "vendor", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/vendor/src", "projectType": "application", "prefix": "vd", "tags": ["type:website"], "implicitDependencies": ["styles", "icon-font"], "targets": {"build": {"executor": "@nx/angular:application", "outputs": ["{options.outputPath.base}"], "options": {"baseHref": "/", "outputPath": {"base": "dist/apps/vendor", "browser": ""}, "index": "apps/vendor/src/index.html", "polyfills": ["zone.js"], "tsConfig": "apps/vendor/tsconfig.app.json", "crossOrigin": "use-credentials", "outputHashing": "all", "preserveSymlinks": true, "progress": false, "allowedCommonJsDependencies": ["@myndmanagement/styles", "date-fns", "deepmerge", "fast-deep-equal", "file-saver", "<PERSON><PERSON><PERSON>", "is-callable", "j<PERSON><PERSON>", "lodash", "lodash.clonedeep", "lodash.isempty", "lodash.isequal", "lzbase62", "<PERSON><PERSON><PERSON><PERSON>", "pusher-js", "qs", "resize-sensor", "rosie", "spacetime", "quill-delta"], "stylePreprocessorOptions": {"sass": {"silenceDeprecations": ["mixed-decls", "color-functions", "global-builtin", "import"]}, "includePaths": ["apps/vendor/src", "packages/styles"]}, "assets": [{"glob": "**/*", "input": "apps/vendor/src/assets", "output": "assets"}, {"glob": "**/*", "input": "packages/styles/assets/images", "output": "assets/images"}, {"glob": "*", "input": "packages/styles/assets/websites", "output": "."}], "styles": ["apps/vendor/src/styles.scss", "node_modules/normalize.css/normalize.css", "dist/packages/icon-font/font.css", "node_modules/ngx-toastr/toastr.css", "packages/styles/styles/toastr.scss", "packages/styles/styles/fix-quill-bullet-list-styles.scss", "node_modules/@angular/cdk/overlay-prebuilt.css", "node_modules/quill/dist/quill.snow.css"], "budgets": [{"type": "anyComponentStyle", "maximumWarning": "7kb"}], "scripts": ["apps/vendor/src/scripts/stonly.js"], "extractLicenses": false, "sourceMap": true, "optimization": false, "namedChunks": true, "browser": "apps/vendor/src/main.ts"}, "configurations": {"prod": {"budgets": [{"type": "initial", "maximumWarning": "3.2mb", "maximumError": "5mb"}, {"type": "anyComponentStyle", "maximumWarning": "7kb"}], "extractLicenses": true, "optimization": true, "sourceMap": true, "namedChunks": false}, "staging": {"optimization": true, "sourceMap": true, "namedChunks": false}, "dev": {}, "local": {"progress": true, "outputHashing": "none"}}, "defaultConfiguration": "prod"}, "serve": {"executor": "@nx/angular:dev-server", "dependsOn": ["styles:build", "icon-font:build"], "options": {"buildTarget": "vendor:build:local", "port": 4218}}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectName}"], "options": {"jestConfig": "apps/vendor/jest.config.ts"}}, "lint": {"executor": "@nx/eslint:lint", "options": {"lintFilePatterns": ["apps/vendor/src", "{projectRoot}/src/**/*.{ts,html}"]}}, "stylelint": {"executor": "nx-stylelint:lint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["apps/vendor/src/**/*.css", "apps/vendor/src/**/*.scss"]}}}}