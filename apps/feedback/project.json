{"name": "feedback", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/feedback/src", "projectType": "application", "prefix": "f", "tags": ["type:website"], "implicitDependencies": ["styles", "icon-font"], "targets": {"build": {"executor": "@nx/angular:application", "outputs": ["{options.outputPath.base}"], "options": {"baseHref": "/", "outputPath": {"base": "dist/apps/feedback", "browser": ""}, "index": "apps/feedback/src/index.html", "polyfills": ["zone.js"], "tsConfig": "apps/feedback/tsconfig.app.json", "crossOrigin": "use-credentials", "outputHashing": "all", "preserveSymlinks": true, "progress": false, "allowedCommonJsDependencies": ["@myndmanagement/styles", "date-fns", "fast-deep-equal", "is-callable", "hellosign-embedded", "j<PERSON><PERSON>", "lodash", "lodash.clonedeep", "lodash.isempty", "lodash.isequal", "monolite", "<PERSON><PERSON><PERSON><PERSON>", "pusher-js", "qs", "quill", "resize-sensor", "rosie", "save-as"], "budgets": [{"type": "anyComponentStyle", "maximumWarning": "16kb"}], "stylePreprocessorOptions": {"sass": {"silenceDeprecations": ["mixed-decls", "color-functions", "global-builtin", "import"]}, "includePaths": ["packages/styles", "apps/feedback/src"]}, "assets": [{"glob": "**/*", "input": "apps/feedback/src/assets", "output": "assets"}, {"glob": "**/*", "input": "packages/styles/assets/images/feedback-rating", "output": "assets/images/feedback-rating"}, {"glob": "*", "input": "packages/styles/assets/websites", "output": "."}, {"glob": "*", "input": "packages/styles/assets/images", "output": "assets/images"}], "styles": ["apps/feedback/src/styles.scss", "node_modules/normalize.css/normalize.css", "dist/packages/icon-font/font.css"], "scripts": [], "extractLicenses": false, "sourceMap": true, "optimization": false, "namedChunks": true, "browser": "apps/feedback/src/main.ts"}, "configurations": {"prod": {"extractLicenses": true, "optimization": true, "sourceMap": true, "namedChunks": false}, "staging": {"optimization": true, "sourceMap": true, "namedChunks": false}, "dev": {}, "local": {"progress": true, "outputHashing": "none"}}, "defaultConfiguration": "prod"}, "serve": {"executor": "@nx/angular:dev-server", "dependsOn": ["styles:build", "icon-font:build"], "options": {"buildTarget": "feedback:build:local", "port": 4216}}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectName}"], "options": {"jestConfig": "apps/feedback/jest.config.ts"}}, "lint": {"executor": "@nx/eslint:lint", "options": {"lintFilePatterns": ["apps/feedback/src", "{projectRoot}/src/**/*.{ts,html}"]}}, "stylelint": {"executor": "nx-stylelint:lint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["apps/feedback/src/**/*.css", "apps/feedback/src/**/*.scss"]}}}}