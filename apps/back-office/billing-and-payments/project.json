{"name": "bo-billing-and-payments", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/back-office/billing-and-payments/src", "projectType": "application", "tags": ["scope:back-office", "type:website"], "implicitDependencies": ["styles", "icon-font"], "targets": {"build:sw": {"executor": "@nx/esbuild:esbuild", "outputs": ["{options.outputPath}"], "options": {"main": "apps/back-office/billing-and-payments/service-worker/service-worker.ts", "outputPath": "dist/apps/back-office/billing-and-payments-sw", "outputFileName": "otto-sw.js", "platform": "browser", "thirdParty": true, "minify": true, "tsConfig": "apps/back-office/billing-and-payments/tsconfig.sw.json"}, "cache": true}, "build": {"executor": "@nx/angular:application", "dependsOn": ["build:sw", "^build"], "options": {"baseHref": "/billing-and-payments/", "outputPath": {"base": "dist/apps/back-office/billing-and-payments", "browser": ""}, "index": "apps/back-office/billing-and-payments/src/index.html", "polyfills": ["apps/back-office/billing-and-payments/src/polyfills.ts"], "tsConfig": "apps/back-office/billing-and-payments/tsconfig.app.json", "crossOrigin": "use-credentials", "outputHashing": "all", "extractLicenses": false, "namedChunks": true, "optimization": false, "preserveSymlinks": true, "progress": false, "sourceMap": true, "allowedCommonJsDependencies": ["@myndmanagement/styles", "angular2-text-mask", "date-fns", "fast-deep-equal", "file-saver", "is-callable", "j<PERSON><PERSON>", "lodash", "lodash.clonedeep", "lodash.isempty", "lodash.isequal", "pusher-js", "qs", "resize-sensor", "rosie", "save-as", "quill-delta", "<PERSON><PERSON><PERSON><PERSON>", "lzbase62"], "assets": [{"glob": "*", "input": "apps/back-office/billing-and-payments/src/assets", "output": "assets"}, {"glob": "otto-sw.js", "input": "dist/apps/back-office/billing-and-payments-sw", "output": "."}, {"glob": "*", "input": "packages/styles/assets/websites", "output": "."}, {"glob": "*", "input": "packages/styles/assets/images", "output": "assets/images"}], "stylePreprocessorOptions": {"sass": {"silenceDeprecations": ["mixed-decls", "color-functions", "global-builtin", "import"]}}, "styles": ["apps/back-office/billing-and-payments/src/styles/styles.scss", "packages/styles/styles/accounting/accounting-layout.scss", "packages/styles/styles/accounting/accounting-filterable-view-page-wrapper.scss", "node_modules/normalize.css/normalize.css", "dist/packages/icon-font/font.css", "node_modules/ngx-toastr/toastr.css", "packages/styles/styles/toastr.scss", "packages/styles/styles/fix-quill-bullet-list-styles.scss", "node_modules/quill/dist/quill.snow.css", "node_modules/@angular/cdk/overlay-prebuilt.css"], "budgets": [{"type": "anyComponentStyle", "maximumWarning": "6kb"}], "browser": "apps/back-office/billing-and-payments/src/main.ts"}, "configurations": {"prod": {"extractLicenses": true, "optimization": true, "sourceMap": true, "namedChunks": false}, "staging": {"optimization": true, "sourceMap": true, "namedChunks": false}, "dev": {}, "local": {"assets": [{"glob": "otto-sw.js", "input": "dist/apps/back-office/billing-and-payments-sw", "output": "."}, {"glob": "*", "input": "apps/back-office/billing-and-payments/src/assets", "output": "assets"}, {"glob": "*", "input": "packages/styles/assets/websites", "output": "."}, {"glob": "*", "input": "packages/styles/assets/images", "output": "assets/images"}], "progress": true, "outputHashing": "none"}}, "defaultConfiguration": "prod", "outputs": ["{options.outputPath.base}"]}, "serve": {"executor": "@nx/angular:dev-server", "dependsOn": ["styles:build", "icon-font:build"], "options": {"proxyConfig": "apps/back-office/billing-and-payments/proxy.conf.json", "buildTarget": "bo-billing-and-payments:build:local", "port": 4203}}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectName}"], "options": {"jestConfig": "apps/back-office/billing-and-payments/jest.config.ts"}}, "lint": {"executor": "@nx/eslint:lint", "options": {"lintFilePatterns": ["apps/back-office/billing-and-payments/src", "{projectRoot}/src/**/*.{ts,html}"]}}, "stylelint": {"executor": "nx-stylelint:lint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["apps/back-office/billing-and-payments/src/**/*.css", "apps/back-office/billing-and-payments/src/**/*.scss"]}}}}