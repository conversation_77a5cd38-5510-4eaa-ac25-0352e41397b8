{"name": "bo-administration", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/back-office/administration/src", "projectType": "application", "tags": ["scope:back-office", "type:website"], "implicitDependencies": ["styles", "icon-font"], "targets": {"build": {"executor": "@nx/angular:application", "options": {"baseHref": "/administration/", "outputPath": {"base": "dist/apps/back-office/administration", "browser": ""}, "index": "apps/back-office/administration/src/index.html", "polyfills": ["zone.js"], "tsConfig": "apps/back-office/administration/tsconfig.app.json", "crossOrigin": "use-credentials", "outputHashing": "all", "preserveSymlinks": true, "progress": false, "allowedCommonJsDependencies": ["@myndmanagement/styles", "angular2-text-mask", "date-fns", "lodash", "lodash.flattendeep", "lodash.isequal", "<PERSON><PERSON><PERSON><PERSON>", "qs", "quill", "resize-sensor", "rosie"], "assets": [{"glob": "*", "input": "apps/back-office/administration/src/assets", "output": "assets"}, {"glob": "*", "input": "packages/styles/assets/websites", "output": "."}, {"glob": "*", "input": "packages/styles/assets/images", "output": "assets/images"}], "styles": ["apps/back-office/administration/src/styles/styles.scss", "node_modules/normalize.css/normalize.css", "dist/packages/icon-font/font.css", "node_modules/ngx-toastr/toastr.css", "packages/styles/styles/toastr.scss", "packages/styles/styles/fix-quill-bullet-list-styles.scss", "node_modules/@angular/cdk/overlay-prebuilt.css"], "stylePreprocessorOptions": {"sass": {"silenceDeprecations": ["mixed-decls", "color-functions", "global-builtin", "import"]}, "includePaths": ["apps/back-office/administration/src/styles"]}, "budgets": [{"type": "anyComponentStyle", "maximumWarning": "6kb"}], "extractLicenses": false, "sourceMap": true, "optimization": false, "namedChunks": true, "browser": "apps/back-office/administration/src/main.ts"}, "configurations": {"prod": {"extractLicenses": true, "optimization": true, "sourceMap": true, "namedChunks": false}, "staging": {"optimization": true, "sourceMap": true, "namedChunks": false}, "dev": {}, "local": {"assets": [{"glob": "*", "input": "apps/back-office/administration/src/assets", "output": "assets"}, {"glob": "*", "input": "packages/styles/assets/websites", "output": "."}, {"glob": "*", "input": "packages/styles/assets/images", "output": "assets/images"}], "progress": true, "outputHashing": "none"}}, "defaultConfiguration": "prod", "outputs": ["{options.outputPath.base}"]}, "serve": {"executor": "@nx/angular:dev-server", "dependsOn": ["styles:build", "icon-font:build"], "options": {"proxyConfig": "apps/back-office/administration/proxy.conf.json", "buildTarget": "bo-administration:build:local", "port": 4201}}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectName}"], "options": {"jestConfig": "apps/back-office/administration/jest.config.ts"}}, "lint": {"executor": "@nx/eslint:lint", "options": {"lintFilePatterns": ["apps/back-office/administration/src"]}}, "stylelint": {"executor": "nx-stylelint:lint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["apps/back-office/administration/src/**/*.css", "apps/back-office/administration/src/**/*.scss"]}}}}