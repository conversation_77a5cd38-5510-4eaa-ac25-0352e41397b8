import { ChangeDetectionStrategy, Component, computed, inject, input, signal } from '@angular/core';
import { rxResource } from '@angular/core/rxjs-interop';
import { FormsModule } from '@angular/forms';
import { myndAttachmentTypes } from '@myndmanagement/api-attachments';
import { MyndFileAPIService } from '@myndmanagement/api-files';
import { MyndCommonModule } from '@myndmanagement/common';
import { myndSortBasic } from '@myndmanagement/common-utils';
import { MyndFileViewerModule, MyndFileViewerService } from '@myndmanagement/file-manager/viewer';
import { MyndFormsModule } from '@myndmanagement/ui-forms';
import { MyndTableModule } from '@myndmanagement/ui-table';
import { MyndToastrService } from '@myndmanagement/ui-toast';
import { finalize, map, tap } from 'rxjs/operators';

import { HoaPanelComponent } from '../../ui/panel/hoa-panel.component';
import { HoaUWFileItem } from '../data-access/hoa-uw-file-item';
import { HoaUWService } from '../data-access/hoa-uw.service';
import { AiScanExtractSignalStore } from '../view/ai-scan-extract.signal.store';

@Component({
  selector: 'hoa-ai-scan-init',
  imports: [
    FormsModule,
    HoaPanelComponent,
    MyndTableModule,
    MyndCommonModule,
    MyndFormsModule,
    MyndFileViewerModule,
  ],
  templateUrl: './ai-scan-init.component.html',
  styleUrl: './ai-scan-init.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [MyndFileViewerService, MyndFileAPIService],
})
export class AiScanInitComponent {
  aiScanExtractStore = inject(AiScanExtractSignalStore);
  hoaUWService = inject(HoaUWService);
  fileViewerService = inject(MyndFileViewerService);
  fileAPIService = inject(MyndFileAPIService);
  toastr = inject(MyndToastrService);

  hoaId = input.required<string>();

  PROMPT_LIMIT = 6000;
  myndAttachmentTypes = myndAttachmentTypes;

  selected = signal<string[]>([]);
  submitted = signal<boolean>(false);
  prompt = signal<string>('');
  fileIdToView = signal<string>(undefined);

  isValid = computed(() => this.submitted() ? this.selected().length > 0 : true);
  atts = computed(() => this.attachments.value());
  extractedTextFileIds = computed(() =>
    this.atts()
      .filter(a => a.extractedTextFileId)
      .map(a => a.extractedTextFileId),
  );
  allSelected = computed(() =>
    this.extractedTextFileIds().length > 0 && this.extractedTextFileIds().every(id => this.selected().includes(id)),
  );

  attachments = rxResource<HoaUWFileItem[], string>({
    request: this.hoaId,
    loader: (params) =>
      this.hoaUWService.getAttachments(params.request)
        .pipe(
          map(res => res.sort((a, b) => myndSortBasic(a.filename, b.filename))),
          this.toastr.catchServerError(),
        ),
  });

  rxFileIdToView = rxResource({
    request: this.fileIdToView,
    loader: (params) =>
      this.fileAPIService.getById(params.request)
        .pipe(
          tap(f => this.fileViewerService.open(f)),
          this.toastr.catchServerError(),
          finalize(() => this.fileIdToView.set(undefined)),
        ),
  });

  toggleSelectAll() {
    this.selected.set(this.allSelected() ? [] : this.extractedTextFileIds());
  }

  toggleSelection(attachment: HoaUWFileItem) {
    if (!attachment.extractedTextFileId) return;
    const id = attachment.extractedTextFileId;
    const current = this.selected();
    this.selected.set(
      current.includes(id)
        ? current.filter(i => i !== id)
        : [...current, id],
    );
  }

  submit() {
    this.submitted.set(true);
    if (!this.isValid()) return;

    const fileIds = this.atts()
      .map(a => a.extractedTextFileId)
      .filter(a => this.selected().some(s => s === a));
    this.aiScanExtractStore.extract(this.hoaId(), fileIds, this.prompt());
  }
}
