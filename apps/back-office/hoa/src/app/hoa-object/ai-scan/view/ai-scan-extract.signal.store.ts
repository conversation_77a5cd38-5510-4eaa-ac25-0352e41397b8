import { inject } from '@angular/core';
import { rxResource } from '@angular/core/rxjs-interop';
import { Requested } from '@myndmanagement/angular-resource-utils';
import { MyndToastrService } from '@myndmanagement/ui-toast';
import { patchState, signalStore, withMethods, withProps, withState } from '@ngrx/signals';
import { of } from 'rxjs';
import { tap } from 'rxjs/operators';

import { HoaUWService } from '../data-access/hoa-uw.service';

interface AIScanExtractState {
  extract: {
    hoaId: string;
    fileIds: string[];
    prompt: string;
  };
  extracted: Requested;
}

const initialState: AIScanExtractState = {
  extract: {
    hoaId: null,
    fileIds: [],
    prompt: '',
  },
  extracted: undefined,
};

export const AiScanExtractSignalStore = signalStore(
  withState(initialState),
  withProps(() => ({
    _hoaUWService: inject(HoaUWService),
    _toastr: inject(MyndToastrService),
  })),
  withProps((state) => ({
    rxExtract: rxResource({
      request: state.extract,
      loader: (params) => {
        const r = params.request;
        if (!r.hoaId || !r.fileIds.length) return of(null);

        return state._hoaUWService.extract({
          hoaId: r.hoaId,
          extractedTextFiles: r.fileIds,
          additionalPrompt: r.prompt,
        })
          .pipe(
            tap(() => patchState(state, { extracted: {} })),
            state._toastr.catchServerError(),
          );
      },
    }),
  })),
  withMethods((state) => ({
    extract: (hoaId: string, fileIds: string[], prompt: string) =>
      patchState(state, { extract: { hoaId, fileIds, prompt } }),
  })),
);
