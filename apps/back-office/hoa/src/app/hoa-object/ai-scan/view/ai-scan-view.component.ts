import { Component, computed, inject } from '@angular/core';
import { MyndCommonModule } from '@myndmanagement/common';
import { myndHoaObjectSignalStore } from '@myndmanagement/hoa-store';

import { AiScanInitComponent } from '../init/ai-scan-init.component';
import { AiScanResultComponent } from '../result/ai-scan-result.component';

import { AiScanExtractSignalStore } from './ai-scan-extract.signal.store';

@Component({
  selector: 'hoa-ai-scan-view',
  templateUrl: './ai-scan-view.component.html',
  styleUrl: './ai-scan-view.component.scss',
  imports: [
    AiScanInitComponent,
    AiScanResultComponent,
    MyndCommonModule,
  ],
  providers: [AiScanExtractSignalStore],
})
export default class AiScanViewComponent {
  hoaObjectStore = inject(myndHoaObjectSignalStore);
  aiScanExtractStore = inject(AiScanExtractSignalStore);

  hoaId = computed(() => this.hoaObjectStore.hoaObject().hoaId);
}
