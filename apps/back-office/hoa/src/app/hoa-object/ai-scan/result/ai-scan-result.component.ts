import { HttpErrorResponse } from '@angular/common/http';
import { Component, computed, effect, inject, input, signal } from '@angular/core';
import { rxResource } from '@angular/core/rxjs-interop';
import { FormsModule } from '@angular/forms';
import { MyndCommonModule } from '@myndmanagement/common';
import { IMyndHoaObject, myndHoaObjectSignalStore, myndTransformHoaObjectResponse } from '@myndmanagement/hoa-store';
import { MyndFormsModule } from '@myndmanagement/ui-forms';
import { MyndTableModule } from '@myndmanagement/ui-table';
import { MyndToastrService } from '@myndmanagement/ui-toast';
import { catchError, map } from 'rxjs/operators';

import { HoaPanelComponent } from '../../ui/panel/hoa-panel.component';
import { HoaUWService } from '../data-access/hoa-uw.service';
import { AiScanExtractSignalStore } from '../view/ai-scan-extract.signal.store';

import { flattenHoaObjectToFields, HoaField } from './utils/flatten-hoa-object-to-fields';

@Component({
  selector: 'hoa-ai-scan-result',
  templateUrl: './ai-scan-result.component.html',
  styleUrl: './ai-scan-result.component.scss',
  imports: [
    HoaPanelComponent,
    MyndTableModule,
    MyndCommonModule,
    MyndFormsModule,
    FormsModule,
  ],
})
export class AiScanResultComponent {
  aiScanExtractStore = inject(AiScanExtractSignalStore);
  hoaObjectStore = inject(myndHoaObjectSignalStore);
  hoaUWService = inject(HoaUWService);
  toastr = inject(MyndToastrService);

  hoaId = input.required<string>();

  selected = signal<number[]>([]);

  items = computed<HoaField[]>(() =>
    flattenHoaObjectToFields(this.hoaObjectStore.hoaObject(), this.extractedData.value()),
  );
  itemIds = computed(() => this.items().map(i => i.id));
  allSelected = computed(() =>
    this.itemIds().every(id => this.selected().includes(id)),
  );

  extractedData = rxResource<IMyndHoaObject, string>({
    request: this.hoaId,
    loader: (params) =>
      this.hoaUWService.getExtractedData(params.request)
        .pipe(
          map(myndTransformHoaObjectResponse),
          catchError((err: HttpErrorResponse) => {
            if (err?.status !== 404) {
              this.toastr.serverError(err);
            }
            throw err;
          }),
        ),
  });

  constructor() {
    effect(() => {
      if (this.aiScanExtractStore.extracted()) {
        this.extractedData.reload();
      }
    });
  }

  toggleSelectAll() {
    this.selected.set(this.allSelected() ? [] : this.itemIds());
  }

  toggleSelection(item: { id: number }) {
    const current = this.selected();
    this.selected.set(
      current.includes(item.id)
        ? current.filter(i => i !== item.id)
        : [...current, item.id],
    );
  }
}
