{"name": "bo-cases", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/back-office/cases/src", "projectType": "application", "tags": ["scope:back-office", "type:website"], "implicitDependencies": ["styles", "icon-font"], "targets": {"build:sw": {"executor": "@nx/esbuild:esbuild", "outputs": ["{options.outputPath}"], "options": {"main": "apps/back-office/cases/service-worker/service-worker.ts", "outputPath": "dist/apps/back-office/cases-sw", "outputFileName": "otto-sw.js", "platform": "browser", "thirdParty": true, "minify": true, "tsConfig": "apps/back-office/cases/tsconfig.sw.json"}, "cache": true}, "build": {"executor": "@nx/angular:application", "outputs": ["{options.outputPath.base}"], "dependsOn": ["build:sw", "^build"], "options": {"baseHref": "/cases/", "outputPath": {"base": "dist/apps/back-office/cases", "browser": ""}, "index": "apps/back-office/cases/src/index.html", "polyfills": ["zone.js"], "tsConfig": "apps/back-office/cases/tsconfig.app.json", "crossOrigin": "use-credentials", "outputHashing": "all", "preserveSymlinks": true, "progress": false, "allowedCommonJsDependencies": ["@myndmanagement/styles", "date-fns", "fast-deep-equal", "is-callable", "j<PERSON><PERSON>", "lodash", "lodash.clonedeep", "lodash.isempty", "lodash.isequal", "lodash.pick", "lodash.flatten", "lodash.values", "lodash.uniqby", "<PERSON><PERSON><PERSON><PERSON>", "pusher-js", "qs", "resize-sensor", "rosie", "save-as", "file-saver", "lzbase62", "quill-delta"], "stylePreprocessorOptions": {"sass": {"silenceDeprecations": ["mixed-decls", "color-functions", "global-builtin", "import"]}, "includePaths": ["packages/styles"]}, "assets": [{"glob": "*", "input": "apps/back-office/cases/src/assets", "output": "assets"}, {"glob": "otto-sw.js", "input": "dist/apps/back-office/cases-sw", "output": "."}, {"glob": "*", "input": "packages/styles/assets/websites", "output": "."}], "styles": ["apps/back-office/cases/src/styles.scss", "node_modules/normalize.css/normalize.css", "dist/packages/icon-font/font.css", "node_modules/ngx-toastr/toastr.css", "packages/styles/styles/toastr.scss", "packages/styles/styles/fix-quill-bullet-list-styles.scss", "node_modules/quill/dist/quill.snow.css", "node_modules/@angular/cdk/overlay-prebuilt.css"], "budgets": [{"type": "anyComponentStyle", "maximumWarning": "6kb"}], "extractLicenses": false, "sourceMap": true, "optimization": false, "namedChunks": true, "browser": "apps/back-office/cases/src/main.ts"}, "configurations": {"prod": {"extractLicenses": true, "optimization": true, "sourceMap": true, "namedChunks": false}, "staging": {"optimization": true, "sourceMap": true, "namedChunks": false}, "dev": {}, "local": {"assets": [{"glob": "*", "input": "apps/back-office/cases/src/assets", "output": "assets"}, {"glob": "otto-sw.js", "input": "dist/apps/back-office/cases-sw", "output": "."}, {"glob": "*", "input": "packages/styles/assets/websites", "output": "."}, {"glob": "*", "input": "packages/styles/assets/images", "output": "assets/images"}], "progress": true, "outputHashing": "none"}}, "defaultConfiguration": "prod"}, "serve": {"executor": "@nx/angular:dev-server", "dependsOn": ["styles:build", "icon-font:build"], "options": {"proxyConfig": "apps/back-office/cases/proxy.conf.json", "buildTarget": "bo-cases:build:local", "port": 4204}}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectName}"], "options": {"jestConfig": "apps/back-office/cases/jest.config.ts"}}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["apps/back-office/cases/src/**/*.{ts,html}"]}}, "stylelint": {"executor": "nx-stylelint:lint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["apps/back-office/cases/src/**/*.css", "apps/back-office/cases/src/**/*.scss"]}}}}