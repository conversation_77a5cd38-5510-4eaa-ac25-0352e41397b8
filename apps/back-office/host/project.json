{"name": "bo-host", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/back-office/host/src", "projectType": "application", "prefix": "app", "tags": ["scope:back-office", "type:website"], "implicitDependencies": ["styles", "icon-font", "back-office-layout"], "targets": {"build": {"executor": "@nx/angular:application", "outputs": ["{options.outputPath.base}"], "options": {"baseHref": "/", "outputPath": {"base": "dist/apps/back-office/host", "browser": ""}, "index": "apps/back-office/host/src/index.html", "polyfills": ["zone.js"], "tsConfig": "apps/back-office/host/tsconfig.app.json", "crossOrigin": "use-credentials", "outputHashing": "all", "preserveSymlinks": true, "progress": false, "assets": [{"glob": "*", "input": "apps/back-office/host/src/assets", "output": "assets"}, {"glob": "*", "input": "packages/styles/assets/websites", "output": "."}, {"glob": "*", "input": "packages/styles/assets/images", "output": "assets/images"}], "stylePreprocessorOptions": {"sass": {"silenceDeprecations": ["mixed-decls", "color-functions", "global-builtin", "import"]}}, "styles": ["apps/back-office/host/src/styles.scss"], "scripts": [], "browser": "apps/back-office/host/src/main.ts"}, "configurations": {"prod": {"budgets": [{"type": "initial", "maximumWarning": "500kb", "maximumError": "1mb"}, {"type": "anyComponentStyle", "maximumWarning": "2kb", "maximumError": "4kb"}], "outputHashing": "all"}, "staging": {"optimization": false, "extractLicenses": false, "sourceMap": true}, "dev": {"optimization": false, "extractLicenses": false, "sourceMap": true}, "local": {"optimization": false, "extractLicenses": false, "sourceMap": true}}, "defaultConfiguration": "prod"}, "serve": {"executor": "@nx/angular:dev-server", "dependsOn": ["styles:build", "icon-font:build"], "options": {"buildTarget": "bo-host:build:local", "port": 4206}}, "lint": {"executor": "@nx/eslint:lint", "options": {"lintFilePatterns": ["apps/back-office/host/src"]}}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectName}"], "options": {"jestConfig": "apps/back-office/host/jest.config.ts"}}, "stylelint": {"executor": "nx-stylelint:lint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["apps/back-office/host/src/**/*.css", "apps/back-office/host/src/**/*.scss"]}}}}