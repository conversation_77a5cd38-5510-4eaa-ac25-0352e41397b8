{"name": "bo-leases", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/back-office/leases/src", "projectType": "application", "tags": ["scope:back-office", "type:website"], "implicitDependencies": ["styles", "icon-font"], "targets": {"build": {"executor": "@nx/angular:application", "outputs": ["{options.outputPath.base}"], "options": {"baseHref": "/leases/", "outputPath": {"base": "dist/apps/back-office/leases", "browser": ""}, "index": "apps/back-office/leases/src/index.html", "polyfills": ["zone.js"], "tsConfig": "apps/back-office/leases/tsconfig.app.json", "crossOrigin": "use-credentials", "outputHashing": "all", "preserveSymlinks": true, "progress": false, "allowedCommonJsDependencies": ["@myndmanagement/styles", "date-fns", "file-saver", "j<PERSON><PERSON>", "lodash", "lodash.clonedeep", "lodash.isequal", "monolite", "<PERSON><PERSON><PERSON><PERSON>", "print-js", "pusher-js", "qs", "resize-sensor", "rosie", "save-as", "quill-delta", "is-callable", "fast-deep-equal"], "stylePreprocessorOptions": {"sass": {"silenceDeprecations": ["mixed-decls", "color-functions", "global-builtin", "import"]}, "includePaths": ["apps/back-office/leases/src"]}, "assets": [{"glob": "*", "input": "apps/back-office/leases/src/assets", "output": "assets"}, {"glob": "*", "input": "packages/styles/assets/websites", "output": "."}], "styles": ["apps/back-office/leases/src/styles.scss", "node_modules/normalize.css/normalize.css", "dist/packages/icon-font/font.css", "node_modules/ngx-toastr/toastr.css", "packages/styles/styles/toastr.scss", "packages/styles/styles/fix-quill-bullet-list-styles.scss", "node_modules/quill/dist/quill.snow.css", "node_modules/@angular/cdk/overlay-prebuilt.css", "node_modules/@fontsource/inter/index.css"], "budgets": [{"type": "anyComponentStyle", "maximumWarning": "7kb"}], "extractLicenses": false, "sourceMap": true, "optimization": false, "namedChunks": true, "browser": "apps/back-office/leases/src/main.ts"}, "configurations": {"prod": {"extractLicenses": true, "optimization": true, "sourceMap": true, "namedChunks": false}, "staging": {"optimization": true, "sourceMap": true, "namedChunks": false}, "dev": {}, "local": {"assets": [{"glob": "*", "input": "apps/back-office/leases/src/assets", "output": "assets"}, {"glob": "*", "input": "packages/styles/assets/websites", "output": "."}, {"glob": "*", "input": "packages/styles/assets/images", "output": "assets/images"}], "progress": true, "outputHashing": "none"}}, "defaultConfiguration": "prod"}, "serve": {"executor": "@nx/angular:dev-server", "dependsOn": ["styles:build", "icon-font:build"], "options": {"proxyConfig": "apps/back-office/leases/proxy.conf.json", "buildTarget": "bo-leases:build:local", "port": 4207}}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectName}"], "options": {"jestConfig": "apps/back-office/leases/jest.config.ts"}}, "lint": {"executor": "@nx/eslint:lint", "options": {"lintFilePatterns": ["apps/back-office/leases/src", "{projectRoot}/src/**/*.{ts,html}"]}}, "stylelint": {"executor": "nx-stylelint:lint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["apps/back-office/leases/src/**/*.css", "apps/back-office/leases/src/**/*.scss"]}}}}