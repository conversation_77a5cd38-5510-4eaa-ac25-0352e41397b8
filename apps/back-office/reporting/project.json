{"name": "bo-reporting", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/back-office/reporting/src", "projectType": "application", "tags": ["scope:back-office", "type:website"], "implicitDependencies": ["styles", "icon-font"], "targets": {"build": {"executor": "@nx/angular:application", "outputs": ["{options.outputPath.base}"], "options": {"baseHref": "/reporting/", "outputPath": {"base": "dist/apps/back-office/reporting", "browser": ""}, "index": "apps/back-office/reporting/src/index.html", "polyfills": ["apps/back-office/reporting/src/polyfills.ts"], "tsConfig": "apps/back-office/reporting/tsconfig.app.json", "crossOrigin": "use-credentials", "outputHashing": "all", "preserveSymlinks": true, "progress": false, "allowedCommonJsDependencies": ["@myndmanagement/styles", "date-fns", "file-saver", "qs", "quill", "resize-sensor", "rosie"], "budgets": [{"type": "anyComponentStyle", "maximumWarning": "6kb"}], "assets": [{"glob": "*", "input": "apps/back-office/reporting/src/assets", "output": "assets"}, {"glob": "*", "input": "packages/styles/assets/websites", "output": "."}], "stylePreprocessorOptions": {"sass": {"silenceDeprecations": ["mixed-decls", "color-functions", "global-builtin", "import"]}}, "styles": ["apps/back-office/reporting/src/styles.scss", "node_modules/normalize.css/normalize.css", "dist/packages/icon-font/font.css", "node_modules/ngx-toastr/toastr.css", "packages/styles/styles/toastr.scss", "packages/styles/styles/fix-quill-bullet-list-styles.scss", "node_modules/@angular/cdk/overlay-prebuilt.css"], "scripts": [], "extractLicenses": false, "sourceMap": true, "optimization": false, "namedChunks": true, "browser": "apps/back-office/reporting/src/main.ts"}, "configurations": {"prod": {"extractLicenses": true, "optimization": true, "sourceMap": true, "namedChunks": false}, "staging": {"optimization": true, "sourceMap": true, "namedChunks": false}, "dev": {}, "local": {"assets": [{"glob": "*", "input": "apps/back-office/reporting/src/assets", "output": "assets"}, {"glob": "*", "input": "packages/styles/assets/websites", "output": "."}, {"glob": "*", "input": "packages/styles/assets/images", "output": "assets/images"}], "progress": true, "outputHashing": "none"}}, "defaultConfiguration": "prod"}, "serve": {"executor": "@nx/angular:dev-server", "dependsOn": ["styles:build", "icon-font:build"], "options": {"proxyConfig": "apps/back-office/reporting/proxy.conf.json", "buildTarget": "bo-reporting:build:local", "port": 4210}}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectName}"], "options": {"jestConfig": "apps/back-office/reporting/jest.config.ts"}}, "lint": {"executor": "@nx/eslint:lint", "options": {"lintFilePatterns": ["{projectRoot}/src/**/*.{ts,html}"]}}, "stylelint": {"executor": "nx-stylelint:lint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["apps/back-office/reporting/src/**/*.css", "apps/back-office/reporting/src/**/*.scss"]}}}}