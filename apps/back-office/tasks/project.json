{"name": "bo-tasks", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/back-office/tasks/src", "projectType": "application", "tags": ["scope:back-office", "type:website"], "implicitDependencies": ["styles", "icon-font"], "targets": {"build:sw": {"executor": "@nx/esbuild:esbuild", "outputs": ["{options.outputPath}"], "options": {"main": "apps/back-office/tasks/service-worker/service-worker.ts", "outputPath": "dist/apps/back-office/tasks-sw", "outputFileName": "otto-sw.js", "platform": "browser", "thirdParty": true, "minify": true, "tsConfig": "apps/back-office/tasks/tsconfig.sw.json"}, "cache": true}, "build": {"executor": "@nx/angular:application", "outputs": ["{options.outputPath.base}"], "dependsOn": ["build:sw", "^build"], "options": {"baseHref": "/tasks/", "outputPath": {"base": "dist/apps/back-office/tasks", "browser": ""}, "index": "apps/back-office/tasks/src/index.html", "polyfills": ["zone.js"], "tsConfig": "apps/back-office/tasks/tsconfig.app.json", "crossOrigin": "use-credentials", "outputHashing": "all", "preserveSymlinks": true, "progress": false, "allowedCommonJsDependencies": ["@myndmanagement/styles", "date-fns", "fast-deep-equal", "is-callable", "j<PERSON><PERSON>", "lodash", "lodash.clonedeep", "lodash.isempty", "lodash.isequal", "<PERSON><PERSON><PERSON><PERSON>", "pusher-js", "qs", "resize-sensor", "rosie", "save-as", "quill-delta", "file-saver", "lzbase62"], "stylePreprocessorOptions": {"sass": {"silenceDeprecations": ["mixed-decls", "color-functions", "global-builtin", "import"]}}, "assets": [{"glob": "*", "input": "apps/back-office/tasks/src/assets", "output": "assets"}, {"glob": "otto-sw.js", "input": "dist/apps/back-office/tasks-sw", "output": "."}, {"glob": "*", "input": "packages/styles/assets/websites", "output": "."}], "styles": ["apps/back-office/tasks/src/styles.scss", "node_modules/normalize.css/normalize.css", "dist/packages/icon-font/font.css", "node_modules/ngx-toastr/toastr.css", "packages/styles/styles/toastr.scss", "packages/styles/styles/fix-quill-bullet-list-styles.scss", "node_modules/quill/dist/quill.snow.css", "node_modules/@angular/cdk/overlay-prebuilt.css"], "budgets": [{"type": "anyComponentStyle", "maximumWarning": "6kb"}], "extractLicenses": false, "sourceMap": true, "optimization": false, "namedChunks": true, "browser": "apps/back-office/tasks/src/main.ts"}, "configurations": {"prod": {"extractLicenses": true, "optimization": true, "sourceMap": true, "namedChunks": false}, "staging": {"optimization": true, "sourceMap": true, "namedChunks": false}, "dev": {}, "local": {"assets": [{"glob": "otto-sw.js", "input": "dist/apps/back-office/tasks-sw", "output": "."}, {"glob": "*", "input": "apps/back-office/tasks/src/assets", "output": "assets"}, {"glob": "*", "input": "packages/styles/assets/websites", "output": "."}, {"glob": "*", "input": "packages/styles/assets/images", "output": "assets/images"}], "progress": true, "outputHashing": "none"}}, "defaultConfiguration": "prod"}, "serve": {"executor": "@nx/angular:dev-server", "dependsOn": ["styles:build", "icon-font:build"], "options": {"proxyConfig": "apps/back-office/tasks/proxy.conf.json", "buildTarget": "bo-tasks:build:local", "port": 4212}}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectName}"], "options": {"jestConfig": "apps/back-office/tasks/jest.config.ts"}}, "lint": {"executor": "@nx/eslint:lint", "options": {"lintFilePatterns": ["apps/back-office/tasks/src", "{projectRoot}/src/**/*.{ts,html}"]}}, "stylelint": {"executor": "nx-stylelint:lint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["apps/back-office/tasks/src/**/*.css", "apps/back-office/tasks/src/**/*.scss"]}}}}