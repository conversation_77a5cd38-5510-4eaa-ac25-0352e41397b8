import { NgModule } from '@angular/core';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { RouterOutlet, Routes, provideRouter, withComponentInputBinding } from '@angular/router';
import { MyndHeaderLayoutComponent, MyndSideMenuModule } from '@myndmanagement/back-office-layout';
import { MyndCommonModule } from '@myndmanagement/common';
import { MyndNotFoundComponent } from '@myndmanagement/common-otto';
import { myndProvideLaunchDarklyClientId } from '@myndmanagement/launch-darkly';
import { MyndLayoutModule } from '@myndmanagement/layout';
import { MyndLayoutOttoModule } from '@myndmanagement/layout-otto';
import { MyndPropertiesModule } from '@myndmanagement/properties';
import { MyndDevtoolsModule } from '@myndmanagement/store-select';
import { MyndModalsModule } from '@myndmanagement/ui-modals';
import { MyndToastModule } from '@myndmanagement/ui-toast';
import { MyndUsersModule } from '@myndmanagement/users';
import { EffectsModule } from '@ngrx/effects';
import { StoreModule } from '@ngrx/store';
import { Angulartics2Module } from 'angulartics2';

import { AppComponent } from './root/app/app.component';
import { escalationSituationGuard } from './root/guards/escalation-situation.guard';

export const routes: Routes = [
  {
    path: 'templates',
    loadChildren:  () => import('./templates/templates.module').then(m => m.TemplatesModule),
  },
  {
    path: 'field-tasks-config',
    loadChildren:  () => import('./field-tasks-config/field-tasks-config.module').then(m => m.FieldTasksConfigModule),
  },
  {
    path: 'escalation/situations',
    loadChildren:  () => import('./escalation-situations/escalations.module').then(m => m.EscalationsModule),
    canActivate: [escalationSituationGuard],
  },
  {
    path: '',
    loadChildren:  () => import('./tasks/tasks.module').then(m => m.TasksModule),
  },
  {
    path: '**',
    component: MyndNotFoundComponent,
  },
];

@NgModule({
  bootstrap: [AppComponent],

  declarations: [
    AppComponent,
  ],

  imports: [
    BrowserAnimationsModule,

    Angulartics2Module.forRoot(),
    MyndCommonModule.forRoot(),
    MyndModalsModule,
    MyndLayoutModule,
    MyndLayoutOttoModule,
    MyndPropertiesModule,
    MyndUsersModule,
    MyndToastModule.forRoot(),

    StoreModule.forRoot({}, {
      runtimeChecks: {
        strictActionWithinNgZone: true,
      },
    }),
    EffectsModule.forRoot([]),
    MyndDevtoolsModule.forRoot(),
    RouterOutlet,
    MyndSideMenuModule,
    MyndHeaderLayoutComponent,
  ],
  providers: [
    provideRouter(routes, withComponentInputBinding()),
    myndProvideLaunchDarklyClientId(),
  ],
})
export class AppModule {}
