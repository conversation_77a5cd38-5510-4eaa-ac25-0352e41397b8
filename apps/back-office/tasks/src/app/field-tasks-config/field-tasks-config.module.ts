import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { ReactiveFormsModule } from '@angular/forms';
import { RouterModule, Routes } from '@angular/router';
import { MyndCommonModule } from '@myndmanagement/common';
import { MyndFilterableViewModule } from '@myndmanagement/filterable-view';
import { MyndFormsOttoModule } from '@myndmanagement/forms-otto';
import { MyndHistoryModule } from '@myndmanagement/history';
import { MyndLayoutModule } from '@myndmanagement/layout';
import { ToggleButtonGroupModule } from '@myndmanagement/service-requests-shared';
import { MyndFormsModule } from '@myndmanagement/ui-forms';
import { MyndModalsModule } from '@myndmanagement/ui-modals';
import { MyndTableModule } from '@myndmanagement/ui-table';
import { MyndTooltipModule } from '@myndmanagement/ui-tooltip';

import { ConfigListComponent } from './components/config-list/config-list.component';
import { DashboardTabs } from './constants/tabs.constant';
import { DashboardComponent } from './containers/dashboard/dashboard.component';

const routes: Routes = [
  {
    path: '',
    component: DashboardComponent,
    children: [
      {
        path: 'all',
        component: ConfigListComponent,
        data: { tab: DashboardTabs.All },
      },
      {
        path: '',
        pathMatch: 'full',
        redirectTo: DashboardTabs.All,
      },
    ],
  },
];

@NgModule({
  declarations: [
    DashboardComponent,
    ConfigListComponent,
  ],
  imports: [
    CommonModule,
    ReactiveFormsModule,
    RouterModule.forChild(routes),

    MyndFormsModule,
    MyndFormsOttoModule,
    MyndTableModule,
    MyndTooltipModule,
    MyndLayoutModule,
    MyndCommonModule,
    MyndModalsModule,
    MyndFilterableViewModule,
    MyndHistoryModule,

    ToggleButtonGroupModule,
  ],
})
export class FieldTasksConfigModule {}
