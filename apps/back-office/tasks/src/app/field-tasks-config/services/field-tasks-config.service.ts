import { Injectable } from '@angular/core';
import { MyndHttpService } from '@myndmanagement/http';
import { Observable } from 'rxjs';

import { IFieldTaskViewDto } from '../interfaces/configuration';

const apiPath = 'v2/task-management/field_task_configuration';

@Injectable({ providedIn: 'root' })
export class FieldTasksConfigService extends MyndHttpService {

  getAggregatedView(): Observable<{ objects: IFieldTaskViewDto[] }> {
    return this.get(`${apiPath}/aggregated-view`);
  }
}
