import { IMyndFieldTaskConfiguration } from '@myndmanagement/api-subtasks';

export enum FieldTaskOrigin {
  TaskTemplate = 'TASK_TEMPLATE',
  WorkFlowEngine = 'WFE',
}

export const fieldTaskOriginMap = new Map<FieldTaskOrigin, string>([
  [FieldTaskOrigin.TaskTemplate, 'Task Template'],
  [FieldTaskOrigin.WorkFlowEngine, 'Work Flow Engine'],
]);

export interface IFieldTaskViewDto { // Copy of backend interface FieldTaskViewDto
  origin: FieldTaskOrigin;
  originName: string;
  originId?: string;
  fieldTaskName: string;
  fieldTaskConfiguration: IMyndFieldTaskConfiguration;
}

export interface IFilters {
  originName?: string;
  fieldTaskName?: string;
}
