<div class="filters">
  <m-filterable-view-filters-v2
    [config]="filtersConfig"
    [filterValues]="filterValues()"
    [filtersTableKey]="'fieldTasksConfig'"
    (setFilters)="onFiltersSetup($event)"
    (updateFilterValues)="updateFilterValues($event)"
  ></m-filterable-view-filters-v2>
</div>

<div class="table-wrapper">
  @if (loadingStatus() === MyndLoadingStatus.Loading || loadingStatus() === MyndLoadingStatus.NotStarted || isRefreshing()) {
    <m-throbber class="failed-wrapper"></m-throbber>
  }
  @else if (loadingStatus() === MyndLoadingStatus.Error) {
    <div class="failed-wrapper">
      <p>Failed to get list</p>
      <m-button [color]="'red'" (click)="refresh()">Try again</m-button>
    </div>
  }
  @else if (loadingStatus() === MyndLoadingStatus.Loaded) {
    <m-table class="standard-theme" [stickyHeader]="true">
      <m-table-row [isHeader]="true">
        <m-table-column>
          Origin Type
        </m-table-column>
        <m-table-column
          [isSorted]="order().field === SortableColumns.OriginName"
          [isSortedAsc]="isSortedAsc(order())"
          (click)="applySorting(SortableColumns.OriginName)"
        >Origin Name</m-table-column>
        <m-table-column
          [isSorted]="order().field === SortableColumns.FieldTaskName"
          [isSortedAsc]="isSortedAsc(order())"
          (click)="applySorting(SortableColumns.FieldTaskName)"
        >Field Task Name</m-table-column>
        <m-table-column
          class="actions-column"
        ></m-table-column>
      </m-table-row>
      @if (items()?.length) {
        @for (item of items(); track $index) {
          <m-table-row>
            <m-table-column>
              {{ item.origin | mMapValue: fieldTaskOriginMap }}
            </m-table-column>
            <m-table-column>
              {{ item.originName }}
            </m-table-column>
            <m-table-column>
              {{ item.fieldTaskName }}
            </m-table-column>
            <m-table-column>
              @if (item.origin === FieldTaskOrigin.TaskTemplate) {
                <a target="_blank" [routerLink]="['/templates', item.originId]">
                  <m-button
                    class="action-edit-warranty"
                    icon="pencil"
                    [color]="'white'"
                    [small]="true"
                    [hasMinWidth]="false"
                  >Edit</m-button>
                </a>
              }
            </m-table-column>
          </m-table-row>
        }
      }
      @else {
        <m-table-row><p class="empty-list">Empty list</p></m-table-row>
      }
    </m-table>
    @if (totalCount(); as totalCount) {
      <m-pagination
        [totalCount]="totalCount"
        [limit]="pagination().limit"
        [limits]="paginationLimits"
        [offset]="pagination().offset"
        (change)="paginate($event)"
      ></m-pagination>
    }
  }
</div>
