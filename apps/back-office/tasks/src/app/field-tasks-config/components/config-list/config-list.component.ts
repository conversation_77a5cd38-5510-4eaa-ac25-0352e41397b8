import { ChangeDetectionStrategy, Component, inject } from '@angular/core';
import { Router } from '@angular/router';
import { MyndLoadingStatus } from '@myndmanagement/common';
import { IMyndPagination, MyndOrder } from '@myndmanagement/common-utils';
import {
  IMyndFilter,
  IMyndFiltersSetup,
  IMyndFilterValues,
  IMyndOrder,
  myndGetFlatRequestParams,
} from '@myndmanagement/filterable-view';

import { filtersConfig } from '../../constants/filters';
import { FieldTaskOrigin, fieldTaskOriginMap } from '../../interfaces/configuration';
import { FieldTasksConfigSignalStore } from '../../store/field-tasks-config.signal.store';

import { SortableColumns } from './columns.config';

@Component({
  selector: 'tm-config-list',
  templateUrl: './config-list.component.html',
  styleUrls: ['./config-list.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [FieldTasksConfigSignalStore],
  standalone: false,
})
export class ConfigListComponent {

  private storeList = inject(FieldTasksConfigSignalStore);
  private router = inject(Router);

  loadingStatus = this.storeList.loadingStatus;
  isRefreshing = this.storeList.isRefreshing;

  items = this.storeList.items;
  pagination = this.storeList.pagination;
  order = this.storeList.order;
  totalCount = this.storeList.totalCount;

  filterValues = this.storeList.filterValues;
  filterValuesFlat = this.storeList.filterValuesFlat;

  readonly filtersConfig = filtersConfig;
  readonly paginationLimits = [10, 15, 25, 50];

  readonly MyndLoadingStatus = MyndLoadingStatus;
  readonly SortableColumns = SortableColumns;
  readonly FieldTaskOrigin = FieldTaskOrigin;
  readonly fieldTaskOriginMap = fieldTaskOriginMap;

  private filters: IMyndFilter[] = [];

  onFiltersSetup({ filters, initialValues }: IMyndFiltersSetup): void {
    this.filters = filters;
    this.updateFilterValues(initialValues);
  }

  updateFilterValues(filterValues: IMyndFilterValues): void {
    const filterValuesFlat = myndGetFlatRequestParams(this.filters, filterValues);
    this.storeList.updateFilterValues(filterValues, filterValuesFlat);
  }

  applySorting(column: SortableColumns): void {
    this.storeList.changeSort(column);
  }

  refresh(): void {
    this.storeList.refresh();
  }

  paginate(pagination: IMyndPagination): void {
    this.storeList.paginate(pagination);
  }

  isSortedAsc(order: IMyndOrder): boolean {
    return order.direction === MyndOrder.Asc;
  }
}
