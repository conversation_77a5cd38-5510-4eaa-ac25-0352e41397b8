import { IMyndFilterableViewFiltersConfig } from '@myndmanagement/filterable-view';
import { mapToFilterOptions } from '@myndmanagement/service-requests-shared';

import { fieldTaskOriginMap } from '../interfaces/configuration';

export const filtersConfig: IMyndFilterableViewFiltersConfig = {
  enableFloatingFilters: true,
  cacheValues: true,
  primary: [
    {
      type: 'text',
      fieldName: 'search',
      label: 'Search',
      textInputIcon: 'search',
      textInputVisibleIcon: true,
    },
    {
      inline: true,
      type: 'multiselect',
      fieldName: 'origin',
      label: 'Origin',
      options: mapToFilterOptions(fieldTaskOriginMap),
      gridColSize: 3,
      componentSpecificOptions: {
        placeholder: 'Select Origin',
      },
    },
  ],
  additional: [],
};
