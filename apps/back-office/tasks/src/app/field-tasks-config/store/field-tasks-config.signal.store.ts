import { HttpErrorResponse } from '@angular/common/http';
import { effect, inject } from '@angular/core';
import { MyndLoadingStatus } from '@myndmanagement/common';
import { IMyndPagination, MyndOrder } from '@myndmanagement/common-utils';
import {
  IMyndFilterValues,
  IMyndOrder,
} from '@myndmanagement/filterable-view';
import { getOppositeOrderDirection } from '@myndmanagement/service-requests-shared';
import { MyndToastrService } from '@myndmanagement/ui-toast';
import { tapResponse } from '@ngrx/operators';
import {
  patchState,
  signalStore,
  withHooks,
  withMethods,
  withState,
} from '@ngrx/signals';
import { rxMethod } from '@ngrx/signals/rxjs-interop';
import { isEqual } from 'lodash';
import { pipe, timer } from 'rxjs';
import { debounceTime, switchMap, tap } from 'rxjs/operators';

import { SortableColumns } from '../components/config-list/columns.config';
import {
  IFieldTaskViewDto,
  IFilters,
} from '../interfaces/configuration';
import { FieldTasksConfigService } from '../services/field-tasks-config.service';

type ConfigurationsState = {
  loadingStatus: MyndLoadingStatus;
  isRefreshing: boolean; // Fake loader for better UI experience
  itemsAll: IFieldTaskViewDto[];
  items: IFieldTaskViewDto[]; // Slice of itemsAll
  totalCount: number;
  pagination: IMyndPagination;
  order: IMyndOrder;

  // filtersValuesRaw: IMyndFilterValues;
  filterValues: IMyndFilterValues;
  filterValuesFlat: IFilters;
};

const initialState: ConfigurationsState = {
  loadingStatus: MyndLoadingStatus.NotStarted,
  isRefreshing: false,
  itemsAll: null,
  items: null,
  totalCount: 0,
  pagination: { limit: 25, offset: 0 },
  order: {
    field: null,
    direction: 'ASC',
  },

  filterValues: {},
  filterValuesFlat: {},
};

export const debouncePeriod = 100;

export const FieldTasksConfigSignalStore = signalStore(
  withState(initialState),
  withMethods((
    state,
    service = inject(FieldTasksConfigService),
    toastr = inject(MyndToastrService),
  ) => ({
    // eslint-disable-next-line no-underscore-dangle
    _loadFullList: rxMethod<void>(
      pipe(
        tap(() => patchState(state, { loadingStatus: MyndLoadingStatus.Loading })),
        debounceTime(debouncePeriod),
        switchMap(() => {
          return service.getAggregatedView().pipe(
            tapResponse({
              next: data => patchState(state, {
                loadingStatus: MyndLoadingStatus.Loaded,
                itemsAll: data.objects,
              }),
              error: (err: HttpErrorResponse) => {
                toastr.serverError(err);
                patchState(state, { itemsAll: null, loadingStatus: MyndLoadingStatus.Error });
              },
            }),
          );
        }),
      ),
    ),

    // eslint-disable-next-line no-underscore-dangle
    _refreshItems(): void {
      const pagination = state.pagination();
      const order = state.order();
      const filterValues = state.filterValues();
      const itemsAll: IFieldTaskViewDto[] = state.itemsAll();

      if (pagination?.limit && order && itemsAll?.length) {
        const search: string = filterValues?.search?.toLowerCase();
        const origins: string[] = filterValues?.origin?.map(orig => orig.key);

        const sorted = [...itemsAll].sort((a: IFieldTaskViewDto, b: IFieldTaskViewDto) => {
          const dir = order.direction === 'ASC' ? 1 : -1;
          if (order.field === SortableColumns.OriginName) {
            return dir * a.originName?.localeCompare(b.originName);
          }
          if (order.field === SortableColumns.FieldTaskName) {
            return dir * a.fieldTaskName?.localeCompare(b.fieldTaskName);
          }
        });

        let filtered = [...sorted];
        if (search) {
          filtered = filtered.filter(item => item.originName?.toLowerCase().indexOf(search) >= 0 || item.fieldTaskName?.toLowerCase().indexOf(search) >= 0);
        }
        if (origins?.length) {
          filtered = filtered.filter(item => origins.includes(item.origin));
        }

        patchState(state, { items: filtered.slice(pagination.offset, pagination.offset + pagination.limit), totalCount: filtered.length, isRefreshing: true });
        timer(300).subscribe(() => {
          patchState(state, { isRefreshing: false });
        });
      } else {
        patchState(state, { items: [] });
      }
    },

    refresh(): void {
      // eslint-disable-next-line no-underscore-dangle
      this._loadFullList();
    },

    paginate(pagination: IMyndPagination): void {
      patchState(state, { pagination });
    },

    updateFilterValues(filterValues: IMyndFilterValues, filterValuesFlat: IFilters): void {
      if (!isEqual(state.filterValuesFlat(), filterValuesFlat)) {
        patchState(state, { filterValues, filterValuesFlat, pagination: { ...state.pagination(), offset: 0 } });
      }
    },

    changeSort(field: string): void {
      const order: IMyndOrder = {
        field,
        direction: state.order().field === field ? getOppositeOrderDirection(state.order().direction) : MyndOrder.Asc,
      };
      return patchState(state, { order });
    },
  })),
  withHooks({
    // eslint-disable-next-line @typescript-eslint/typedef
    onInit: (
      store,
    ) => {
      effect(() => {
        // It's self-calling method :) Effect is triggered when one of signals
        //  inside method (order, pagination, filter) is changed.
        // eslint-disable-next-line no-underscore-dangle
        store._refreshItems();
      });
      // Load list first and last time
      store._loadFullList();
    },
  }),
);
