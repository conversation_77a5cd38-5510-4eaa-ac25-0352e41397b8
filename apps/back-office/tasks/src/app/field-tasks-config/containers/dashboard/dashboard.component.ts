import { ChangeDetectionStrategy, Component } from '@angular/core';

import { DashboardTabs } from '../../constants/tabs.constant';

const allTabs = [
  { key: DashboardTabs.All, name: 'All' },
];

@Component({
  selector: 'tm-dashboard',
  templateUrl: './dashboard.component.html',
  styleUrls: ['./dashboard.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  standalone: false,
})
export class DashboardComponent {
  readonly tabs = allTabs;
}
