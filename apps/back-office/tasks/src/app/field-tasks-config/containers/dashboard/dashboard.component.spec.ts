import { RouterModule } from '@angular/router';
import { IMyndBoUser } from '@myndmanagement/api-bo-users';
import { MyndCommonModule } from '@myndmanagement/common';
import { MyndCommonOttoModule } from '@myndmanagement/common-otto';
import { SharedModule } from '@myndmanagement/service-requests-shared';
import { myndProvideToastrServiceMock } from '@myndmanagement/ui-toast/testing';
import { myndSelectMe } from '@myndmanagement/users';
import { createComponentFactory, Spectator } from '@ngneat/spectator/jest';
import { EffectsModule } from '@ngrx/effects';
import { StoreModule } from '@ngrx/store';
import { provideMockStore } from '@ngrx/store/testing';

import { DashboardComponent } from './dashboard.component';

const mockUser: IMyndBoUser = {
  boUserId: 'testBoUserId',
  externalId: 'testExternalId',
  firstName: 'TestFirstName',
  lastName: 'TestLastName',
  email: 'testEmailAddress',
  createdAt: 'testCreatedAt',
  updatedAt: 'testUpdatedAt',
  lastVisitedAt: 'testLastVisitedAt',
};

describe('ServiceRequestsDashboardComponent', () => {
  let spectator: Spectator<DashboardComponent>;
  const createComponent = createComponentFactory({
    component: DashboardComponent,
    imports: [
      RouterModule.forRoot([]),
      MyndCommonModule.forRoot(),
      MyndCommonOttoModule,
      SharedModule,
      StoreModule.forRoot({}),
      EffectsModule.forRoot(),
    ],
    providers: [
      myndProvideToastrServiceMock(),
      provideMockStore({
        initialState: {
          myndMe: {},
        },
        selectors: [
          { selector: myndSelectMe, value: mockUser },
        ],
      }),
    ],
  });

  beforeEach(() => {
    spectator = createComponent();
  });

  it('should see tabs', () => {
    spectator.detectChanges();
    expect(spectator.query('m-folder-tab-bar')).toExist();
  });

});
