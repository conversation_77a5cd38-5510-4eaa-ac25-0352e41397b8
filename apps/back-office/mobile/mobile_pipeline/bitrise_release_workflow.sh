#!/usr/bin/env bash

set -ex

export ANDROID_KEYSTORE_PATH=$(pwd)/keystore.jks
export SERVICE_ACCOUNT_JSON_KEY=$(pwd)/service_account_key.json
export APP_STORE_CONNECT_API_KEY_PATH=$(pwd)/app_store_connect_api_key.json
export GOOGLE_SERVICES_JSON=$(pwd)/android/app/google-services.json

# Set ENVIRONMENT to a default value if it is not set or is empty
export ENVIRONMENT=${ENVIRONMENT:-"staging"}

if [[ $ENVIRONMENT = staging ]]; then
  export ANDROID_KEYSTORE_PASSWORD=$ANDROID_KEYSTORE_PASSWORD_STG
  export ANDROID_KEYSTORE_URL=$BITRISEIO_ANDROID_KEYSTORE_STG_URL
  export GOOGLE_SERVICES_JSON_URL=$BITRISEIO_GOOGLE_SERVICES_JSON_STG_URL
elif [[ $ENVIRONMENT = prod ]]; then
  export ANDROID_KEYSTORE_PASSWORD=$ANDROID_KEYSTORE_PASSWORD_PROD
  export ANDROID_KEYSTORE_URL=$BITRISEIO_ANDROID_KEYSTORE_PROD_URL
  export GOOGLE_SERVICES_JSON_URL=$BITRISEIO_GOOGLE_SERVICES_JSON_PROD_URL
fi

curl $ANDROID_KEYSTORE_URL -o $ANDROID_KEYSTORE_PATH
curl $BITRISEIO_SERVICE_ACCOUNT_JSON_KEY_URL -o $SERVICE_ACCOUNT_JSON_KEY
curl $BITRISEIO_APP_STORE_CONNECT_API_KEY_URL -o $APP_STORE_CONNECT_API_KEY_PATH
curl $GOOGLE_SERVICES_JSON_URL -o $GOOGLE_SERVICES_JSON

npm i
npm run build:$ENVIRONMENT
. ./configure_build.sh


bundle update
### BUILD iOS
bundle exec fastlane ios build --verbose
### BUILD Android
bundle exec fastlane android build --verbose

# package artifacts
( find ./ios -type f -name "*.ipa"; find ./android -type f -name "*.apk" ) | xargs -I {} zip artifacts.zip "{}"
