{"name": "bo-mobile", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/back-office/mobile/src", "projectType": "application", "prefix": "mo", "tags": ["scope:back-office", "type:website"], "implicitDependencies": ["styles", "icon-font"], "targets": {"build": {"executor": "@nx/angular:application", "outputs": ["{options.outputPath.base}"], "options": {"baseHref": "/mobile/", "outputPath": {"base": "dist/apps/back-office/mobile", "browser": ""}, "index": "apps/back-office/mobile/src/index.html", "polyfills": ["zone.js"], "tsConfig": "apps/back-office/mobile/tsconfig.app.json", "crossOrigin": "use-credentials", "outputHashing": "all", "preserveSymlinks": true, "progress": false, "serviceWorker": "apps/back-office/mobile/ngsw-config.json", "allowedCommonJsDependencies": ["@myndmanagement/styles", "date-fns", "<PERSON><PERSON><PERSON>", "j<PERSON><PERSON>", "lodash", "qs", "resize-sensor", "stream", "save-as", "lodash.isequal", "pusher-js", "<PERSON><PERSON><PERSON><PERSON>", "rosie", "fast-deep-equal", "is-callable", "localforage", "lodash.clonedeep", "quill"], "stylePreprocessorOptions": {"sass": {"silenceDeprecations": ["mixed-decls", "color-functions", "global-builtin", "import"]}, "includePaths": ["apps/back-office/mobile/src"]}, "assets": [{"glob": "**/*", "input": "apps/back-office/mobile/src/assets", "output": "assets"}, {"glob": "manifest.json", "input": "apps/back-office/mobile/src", "output": "."}, {"glob": "**/*", "input": "packages/styles/assets/images", "output": "assets/images"}, {"glob": "*", "input": "packages/styles/assets/websites", "output": "."}, {"glob": "*", "input": "packages/styles/assets/manifest", "output": "assets/manifest"}], "styles": ["apps/back-office/mobile/src/styles.scss", "node_modules/normalize.css/normalize.css", "dist/packages/icon-font/font.css", "node_modules/ngx-toastr/toastr.css", "packages/styles/styles/toastr.scss", "packages/styles/styles/fix-quill-bullet-list-styles.scss", "node_modules/@angular/cdk/overlay-prebuilt.css", "node_modules/@ionic/angular/css/core.css", "node_modules/@ionic/angular/css/display.css", "node_modules/@ionic/angular/css/float-elements.css"], "scripts": ["node_modules/hammerjs/hammer.min.js"], "budgets": [{"type": "anyComponentStyle", "maximumWarning": "6kb"}], "extractLicenses": false, "sourceMap": true, "optimization": false, "namedChunks": true, "browser": "apps/back-office/mobile/src/main.ts"}, "configurations": {"prod": {"extractLicenses": true, "optimization": true, "sourceMap": true, "namedChunks": false}, "staging": {"optimization": true, "sourceMap": true, "namedChunks": false}, "dev": {}, "local": {"progress": true, "outputHashing": "none"}}, "defaultConfiguration": "prod"}, "serve": {"executor": "@nx/angular:dev-server", "dependsOn": ["styles:build", "icon-font:build"], "options": {"buildTarget": "bo-mobile:build:local", "port": 4209}}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectName}"], "options": {"jestConfig": "apps/back-office/mobile/jest.config.ts"}}, "lint": {"executor": "@nx/eslint:lint", "options": {"lintFilePatterns": ["apps/back-office/mobile/src", "{projectRoot}/src/**/*.{ts,html}"]}}, "stylelint": {"executor": "nx-stylelint:lint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["apps/back-office/mobile/src/**/*.css", "apps/back-office/mobile/src/**/*.scss"]}}}}