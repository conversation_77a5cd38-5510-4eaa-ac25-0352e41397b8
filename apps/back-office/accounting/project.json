{"name": "bo-accounting", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/back-office/accounting/src", "projectType": "application", "tags": ["scope:back-office", "type:website"], "implicitDependencies": ["styles", "icon-font"], "targets": {"build:sw": {"executor": "@nx/esbuild:esbuild", "outputs": ["{options.outputPath}"], "options": {"main": "apps/back-office/accounting/service-worker/service-worker.ts", "outputPath": "dist/apps/back-office/accounting-sw", "outputFileName": "otto-sw.js", "platform": "browser", "thirdParty": true, "minify": true, "tsConfig": "apps/back-office/accounting/tsconfig.sw.json"}, "cache": true}, "build": {"executor": "@nx/angular:application", "dependsOn": ["build:sw", "^build"], "outputs": ["{options.outputPath.base}"], "options": {"baseHref": "/accounting/", "outputPath": {"base": "dist/apps/back-office/accounting", "browser": ""}, "index": "apps/back-office/accounting/src/index.html", "polyfills": ["apps/back-office/accounting/src/polyfills.ts"], "tsConfig": "apps/back-office/accounting/tsconfig.app.json", "crossOrigin": "use-credentials", "outputHashing": "all", "extractLicenses": false, "namedChunks": true, "optimization": false, "preserveSymlinks": true, "progress": false, "sourceMap": true, "allowedCommonJsDependencies": ["@myndmanagement/styles", "core-js", "date-fns", "fast-deep-equal", "file-saver", "is-callable", "jspdf", "jspdf-autotable", "j<PERSON><PERSON>", "lodash", "lodash.clonedeep", "lodash.isempty", "lodash.isequal", "pusher-js", "qs", "raf", "resize-sensor", "rgbcolor", "rosie", "save-as", "lzbase62", "<PERSON><PERSON><PERSON><PERSON>", "quill-delta"], "stylePreprocessorOptions": {"sass": {"silenceDeprecations": ["mixed-decls", "color-functions", "global-builtin", "import"]}, "includePaths": ["node_modules"]}, "assets": [{"glob": "*", "input": "apps/back-office/accounting/src/assets", "output": "assets"}, {"glob": "otto-sw.js", "input": "dist/apps/back-office/accounting-sw", "output": "."}, {"glob": "*", "input": "packages/styles/assets/websites", "output": "."}, {"glob": "*", "input": "packages/styles/assets/images", "output": "assets/images"}], "styles": ["apps/back-office/accounting/src/styles/styles.scss", "packages/styles/styles/accounting/accounting-layout.scss", "packages/styles/styles/accounting/accounting-filterable-view-page-wrapper.scss", "node_modules/normalize.css/normalize.css", "dist/packages/icon-font/font.css", "node_modules/ngx-toastr/toastr.css", "packages/styles/styles/toastr.scss", "packages/styles/styles/fix-quill-bullet-list-styles.scss", "node_modules/quill/dist/quill.snow.css", "node_modules/@angular/cdk/overlay-prebuilt.css"], "budgets": [{"type": "anyComponentStyle", "maximumWarning": "6kb"}], "browser": "apps/back-office/accounting/src/main.ts"}, "configurations": {"prod": {"extractLicenses": true, "optimization": true, "sourceMap": true, "namedChunks": false}, "staging": {"optimization": true, "sourceMap": true, "namedChunks": false}, "dev": {}, "local": {"assets": [{"glob": "otto-sw.js", "input": "dist/apps/back-office/accounting-sw", "output": "."}, {"glob": "*", "input": "apps/back-office/accounting/src/assets", "output": "assets"}, {"glob": "*", "input": "packages/styles/assets/websites", "output": "."}, {"glob": "*", "input": "packages/styles/assets/images", "output": "assets/images"}], "progress": true, "outputHashing": "none"}}, "defaultConfiguration": "prod"}, "serve": {"executor": "@nx/angular:dev-server", "dependsOn": ["styles:build", "icon-font:build"], "options": {"proxyConfig": "apps/back-office/accounting/proxy.conf.json", "buildTarget": "bo-accounting:build:local", "port": 4200}}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectName}"], "options": {"jestConfig": "apps/back-office/accounting/jest.config.ts"}}, "lint": {"executor": "@nx/eslint:lint", "options": {"lintFilePatterns": ["apps/back-office/accounting/src", "{projectRoot}/src/**/*.{ts,html}"]}}, "stylelint": {"executor": "nx-stylelint:lint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["apps/back-office/accounting/src/**/*.css", "apps/back-office/accounting/src/**/*.scss"]}}}}