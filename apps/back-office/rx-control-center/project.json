{"name": "bo-rx-control-center", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/back-office/rx-control-center/src", "projectType": "application", "prefix": "app", "tags": ["scope:back-office", "type:website"], "implicitDependencies": ["styles", "icon-font"], "targets": {"build": {"executor": "@nx/angular:application", "options": {"baseHref": "/rx-control-center/", "outputPath": {"base": "dist/apps/back-office/rx-control-center", "browser": ""}, "index": "apps/back-office/rx-control-center/src/index.html", "polyfills": ["zone.js"], "tsConfig": "apps/back-office/rx-control-center/tsconfig.app.json", "crossOrigin": "use-credentials", "outputHashing": "all", "preserveSymlinks": true, "progress": false, "allowedCommonJsDependencies": ["@myndmanagement/styles", "date-fns", "fast-deep-equal", "is-callable", "hellosign-embedded", "j<PERSON><PERSON>", "lodash", "lodash.clonedeep", "lodash.isempty", "lodash.isequal", "lzbase62", "monolite", "<PERSON><PERSON><PERSON><PERSON>", "pusher-js", "qs", "resize-sensor", "rosie", "save-as", "file-saver", "quill-delta"], "stylePreprocessorOptions": {"sass": {"silenceDeprecations": ["mixed-decls", "color-functions", "global-builtin", "import"]}, "includePaths": ["apps/back-office/rx-control-center/src/styles"]}, "assets": [{"glob": "*", "input": "apps/back-office/rx-control-center/src/assets", "output": "assets"}, {"glob": "*", "input": "packages/styles/assets/websites", "output": "."}, {"glob": "*", "input": "packages/styles/assets/images", "output": "assets/images"}], "styles": ["apps/back-office/rx-control-center/src/styles.scss", "node_modules/normalize.css/normalize.css", "dist/packages/icon-font/font.css", "node_modules/ngx-toastr/toastr.css", "packages/styles/styles/toastr.scss", "packages/styles/styles/fix-quill-bullet-list-styles.scss", "node_modules/quill/dist/quill.snow.css", "node_modules/@angular/cdk/overlay-prebuilt.css"], "budgets": [{"type": "anyComponentStyle", "maximumWarning": "8.5kb"}], "extractLicenses": false, "sourceMap": true, "optimization": false, "namedChunks": true, "browser": "apps/back-office/rx-control-center/src/main.ts"}, "configurations": {"prod": {"budgets": [{"type": "initial", "maximumWarning": "5mb", "maximumError": "5.5mb"}, {"type": "anyComponentStyle", "maximumWarning": "8.5kb"}], "extractLicenses": true, "optimization": true, "sourceMap": true, "namedChunks": false}, "staging": {"optimization": true, "sourceMap": true, "namedChunks": false}, "dev": {}, "local": {"assets": [{"glob": "*", "input": "apps/back-office/rx-control-center/src/assets", "output": "assets"}, {"glob": "*", "input": "packages/styles/assets/websites", "output": "."}, {"glob": "*", "input": "packages/styles/assets/images", "output": "assets/images"}], "progress": true, "outputHashing": "none"}}, "defaultConfiguration": "prod", "outputs": ["{options.outputPath.base}"]}, "serve": {"executor": "@nx/angular:dev-server", "dependsOn": ["styles:build", "icon-font:build"], "options": {"proxyConfig": "apps/back-office/rx-control-center/proxy.conf.json", "buildTarget": "bo-rx-control-center:build:local", "port": 4211}}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectName}"], "options": {"jestConfig": "apps/back-office/rx-control-center/jest.config.ts"}}, "lint": {"executor": "@nx/eslint:lint", "options": {"lintFilePatterns": ["apps/back-office/rx-control-center/src", "{projectRoot}/src/**/*.{ts,html}"]}}, "stylelint": {"executor": "nx-stylelint:lint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["apps/back-office/rx-control-center/src/**/*.css", "apps/back-office/rx-control-center/src/**/*.scss"]}}}}