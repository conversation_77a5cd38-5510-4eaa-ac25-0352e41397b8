{"name": "bo-back-office", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/back-office/back-office/src", "projectType": "application", "tags": ["scope:back-office", "type:website"], "implicitDependencies": ["styles", "icon-font"], "targets": {"build": {"executor": "@nx/angular:application", "outputs": ["{options.outputPath.base}"], "options": {"baseHref": "/back-office/", "outputPath": {"base": "dist/apps/back-office/back-office", "browser": ""}, "index": "apps/back-office/back-office/src/index.html", "polyfills": ["zone.js"], "tsConfig": "apps/back-office/back-office/tsconfig.app.json", "crossOrigin": "use-credentials", "outputHashing": "all", "preserveSymlinks": true, "progress": false, "allowedCommonJsDependencies": ["@myndmanagement/styles", "angular2-text-mask", "date-fns", "date-fns-timezone", "fast-deep-equal", "file-saver", "is-callable", "j<PERSON><PERSON>", "lodash", "lodash.clonedeep", "lodash.isempty", "lodash.isequal", "lzbase62", "<PERSON><PERSON><PERSON><PERSON>", "qs", "resize-sensor", "rosie", "save-as", "smartystreets-javascript-sdk", "quill-delta", "pusher-js"], "stylePreprocessorOptions": {"sass": {"silenceDeprecations": ["mixed-decls", "color-functions", "global-builtin", "import"]}, "includePaths": ["apps/back-office/back-office/src", "node_modules"]}, "assets": [{"glob": "*", "input": "apps/back-office/back-office/src/assets", "output": "assets"}, {"glob": "*", "input": "packages/styles/assets/websites", "output": "."}], "styles": ["apps/back-office/back-office/src/styles.scss", "node_modules/normalize.css/normalize.css", "dist/packages/icon-font/font.css", "node_modules/ngx-toastr/toastr.css", "packages/styles/styles/toastr.scss", "packages/styles/styles/fix-quill-bullet-list-styles.scss", "node_modules/quill/dist/quill.snow.css", "node_modules/@angular/cdk/overlay-prebuilt.css", "node_modules/@fontsource/inter/index.css"], "browser": "apps/back-office/back-office/src/main.ts"}, "configurations": {"prod": {"sourceMap": true, "budgets": [{"type": "anyComponentStyle", "maximumWarning": "7kb"}]}, "staging": {"sourceMap": true, "namedChunks": true}, "dev": {"outputHashing": "none", "optimization": false, "extractLicenses": false, "sourceMap": true, "namedChunks": true}, "local": {"optimization": false, "extractLicenses": false, "sourceMap": true, "namedChunks": true, "assets": [{"glob": "*", "input": "apps/back-office/back-office/src/assets", "output": "assets"}, {"glob": "*", "input": "packages/styles/assets/websites", "output": "."}, {"glob": "*", "input": "packages/styles/assets/images", "output": "assets/images"}], "progress": true, "outputHashing": "none"}}, "defaultConfiguration": "prod"}, "serve": {"executor": "@nx/angular:dev-server", "dependsOn": ["styles:build", "icon-font:build"], "options": {"proxyConfig": "apps/back-office/back-office/proxy.conf.json", "buildTarget": "bo-back-office:build:local", "port": 4202}}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectName}"], "options": {"jestConfig": "apps/back-office/back-office/jest.config.ts"}}, "lint": {"executor": "@nx/eslint:lint", "options": {"lintFilePatterns": ["apps/back-office/back-office/src", "{projectRoot}/src/**/*.{ts,html}"]}}, "stylelint": {"executor": "nx-stylelint:lint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["apps/back-office/back-office/src/**/*.css", "apps/back-office/back-office/src/**/*.scss"]}}}}