{"name": "bo-service-requests", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/back-office/service-requests/src", "projectType": "application", "tags": ["scope:back-office", "type:website"], "implicitDependencies": ["styles", "icon-font"], "targets": {"build": {"executor": "@nx/angular:application", "outputs": ["{options.outputPath.base}"], "options": {"baseHref": "/service-requests/", "outputPath": {"base": "dist/apps/back-office/service-requests", "browser": ""}, "index": "apps/back-office/service-requests/src/index.html", "polyfills": ["zone.js"], "tsConfig": "apps/back-office/service-requests/tsconfig.app.json", "crossOrigin": "use-credentials", "outputHashing": "all", "extractLicenses": false, "sourceMap": true, "optimization": false, "namedChunks": true, "preserveSymlinks": true, "progress": false, "allowedCommonJsDependencies": ["@myndmanagement/styles", "angular2-text-mask", "date-fns", "fast-deep-equal", "file-saver", "is-callable", "j<PERSON><PERSON>", "lodash", "<PERSON><PERSON><PERSON><PERSON>", "pusher-js", "qs", "resize-sensor", "rosie", "stream", "quill-delta", "lodash.isempty", "lodash.clonedeep", "lodash.isequal", "lzbase62"], "stylePreprocessorOptions": {"sass": {"silenceDeprecations": ["mixed-decls", "color-functions", "global-builtin", "import"]}}, "assets": [{"glob": "*", "input": "apps/back-office/service-requests/src/assets", "output": "assets"}, {"glob": "*", "input": "packages/styles/assets/websites", "output": "."}], "styles": ["apps/back-office/service-requests/src/styles.scss", "node_modules/normalize.css/normalize.css", "dist/packages/icon-font/font.css", "node_modules/ngx-toastr/toastr.css", "packages/styles/styles/toastr.scss", "packages/styles/styles/fix-quill-bullet-list-styles.scss", "node_modules/quill/dist/quill.snow.css", "node_modules/@angular/cdk/overlay-prebuilt.css", "node_modules/js-draw/src/Editor.scss", "node_modules/@melloware/coloris/dist/coloris.css"], "budgets": [{"type": "initial", "maximumWarning": "4mb", "maximumError": "24mb"}, {"type": "anyComponentStyle", "maximumWarning": "18kb"}], "browser": "apps/back-office/service-requests/src/main.ts"}, "configurations": {"prod": {"extractLicenses": true, "optimization": true, "sourceMap": true, "namedChunks": false}, "staging": {"optimization": true, "sourceMap": true, "namedChunks": false}, "dev": {}, "local": {"assets": [{"glob": "*", "input": "apps/back-office/service-requests/src/assets", "output": "assets"}, {"glob": "*", "input": "packages/styles/assets/websites", "output": "."}, {"glob": "*", "input": "packages/styles/assets/images", "output": "assets/images"}], "optimization": false, "progress": true, "sourceMap": true, "outputHashing": "none"}}, "defaultConfiguration": "prod"}, "serve": {"executor": "@nx/angular:dev-server", "dependsOn": ["styles:build", "icon-font:build"], "options": {"proxyConfig": "apps/back-office/service-requests/proxy.conf.json", "buildTarget": "bo-service-requests:build:local", "port": 4219}}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectName}"], "options": {"jestConfig": "apps/back-office/service-requests/jest.config.ts"}}, "lint": {"executor": "@nx/eslint:lint", "options": {"lintFilePatterns": ["apps/back-office/service-requests/src", "{projectRoot}/src/**/*.{ts,html}"]}}, "stylelint": {"executor": "nx-stylelint:lint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["apps/back-office/service-requests/src/**/*.css", "apps/back-office/service-requests/src/**/*.scss"]}}}}