{"name": "bo-vendors", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/back-office/vendors/src", "projectType": "application", "prefix": "vd", "tags": ["scope:back-office", "type:website"], "implicitDependencies": ["styles", "icon-font"], "targets": {"build": {"executor": "@nx/angular:application", "outputs": ["{options.outputPath.base}"], "options": {"baseHref": "/vendors/", "outputPath": {"base": "dist/apps/back-office/vendors", "browser": ""}, "index": "apps/back-office/vendors/src/index.html", "polyfills": ["zone.js"], "tsConfig": "apps/back-office/vendors/tsconfig.app.json", "crossOrigin": "use-credentials", "outputHashing": "all", "preserveSymlinks": true, "progress": false, "allowedCommonJsDependencies": ["@myndmanagement/styles", "date-fns", "resize-sensor"], "assets": [{"glob": "*", "input": "apps/back-office/vendors/src/assets", "output": "assets"}, {"glob": "*", "input": "packages/styles/assets/websites", "output": "."}], "stylePreprocessorOptions": {"sass": {"silenceDeprecations": ["mixed-decls", "color-functions", "global-builtin", "import"]}}, "styles": ["apps/back-office/vendors/src/styles.scss", "node_modules/normalize.css/normalize.css", "dist/packages/icon-font/font.css", "node_modules/ngx-toastr/toastr.css", "packages/styles/styles/toastr.scss", "packages/styles/styles/fix-quill-bullet-list-styles.scss", "node_modules/@angular/cdk/overlay-prebuilt.css"], "scripts": [], "budgets": [{"type": "anyComponentStyle", "maximumWarning": "6kb"}], "extractLicenses": false, "sourceMap": true, "optimization": false, "namedChunks": true, "browser": "apps/back-office/vendors/src/main.ts"}, "configurations": {"prod": {"extractLicenses": true, "optimization": true, "sourceMap": true, "namedChunks": false, "budgets": [{"type": "initial", "maximumWarning": "2mb", "maximumError": "5mb"}, {"type": "anyComponentStyle", "maximumWarning": "6kb"}]}, "staging": {"optimization": true, "sourceMap": true, "namedChunks": false}, "dev": {}, "local": {"assets": [{"glob": "*", "input": "apps/back-office/vendors/src/assets", "output": "assets"}, {"glob": "*", "input": "packages/styles/assets/websites", "output": "."}, {"glob": "*", "input": "packages/styles/assets/images", "output": "assets/images"}], "progress": true, "outputHashing": "none"}}, "defaultConfiguration": "prod"}, "serve": {"executor": "@nx/angular:dev-server", "dependsOn": ["styles:build", "icon-font:build"], "options": {"proxyConfig": "apps/back-office/vendors/proxy.conf.json", "buildTarget": "bo-vendors:build:local", "port": 4213}}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectName}"], "options": {"jestConfig": "apps/back-office/vendors/jest.config.ts"}}, "lint": {"executor": "@nx/eslint:lint", "options": {"lintFilePatterns": ["apps/back-office/vendors/src", "{projectRoot}/src/**/*.{ts,html}"]}}, "stylelint": {"executor": "nx-stylelint:lint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["apps/back-office/vendors/src/**/*.css", "apps/back-office/vendors/src/**/*.scss"]}}}}