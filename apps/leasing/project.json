{"name": "leasing", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/leasing/src", "projectType": "application", "tags": ["type:website"], "implicitDependencies": ["styles", "icon-font"], "targets": {"build": {"executor": "@nx/angular:application", "options": {"baseHref": "/", "outputPath": {"base": "dist/apps/leasing", "browser": ""}, "index": "apps/leasing/src/index.html", "polyfills": ["apps/leasing/src/polyfills.ts"], "tsConfig": "apps/leasing/tsconfig.app.json", "outputHashing": "all", "preserveSymlinks": true, "progress": false, "allowedCommonJsDependencies": ["@myndmanagement/styles", "date-fns", "angular2-text-mask", "resize-sensor", "onfido-sdk-ui", "buffer", "js-cookie", "crypto-js", "hellosign-embedded", "lodash", "isomorphic-unfetch", "lodash.isequal", "quill", "pusher-js", "<PERSON><PERSON><PERSON><PERSON>", "rosie", "fast-deep-equal", "is-callable", "@aws-crypto/sha256-js", "mobile-detect"], "stylePreprocessorOptions": {"sass": {"silenceDeprecations": ["mixed-decls", "color-functions", "global-builtin", "import"]}, "includePaths": ["packages/styles"]}, "assets": [{"glob": "**/*", "input": "apps/leasing/src/assets", "output": "assets"}, {"glob": "manifest.json", "input": "apps/leasing/src", "output": "."}, {"glob": "**/*", "input": "packages/styles/assets/images", "output": "assets/images"}, {"glob": "*", "input": "packages/styles/assets/websites", "output": "."}, {"glob": "*", "input": "packages/styles/assets/manifest", "output": "assets/manifest"}], "styles": ["apps/leasing/src/styles.scss", "node_modules/normalize.css/normalize.css", "dist/packages/icon-font/font.css", "node_modules/ngx-toastr/toastr.css", "packages/styles/styles/toastr.scss", "packages/styles/styles/fix-quill-bullet-list-styles.scss", "node_modules/@angular/cdk/overlay-prebuilt.css"], "scripts": [], "serviceWorker": "apps/leasing/ngsw-config.json", "extractLicenses": false, "sourceMap": true, "optimization": false, "namedChunks": true, "browser": "apps/leasing/src/main.ts"}, "configurations": {"prod": {"extractLicenses": true, "optimization": true, "sourceMap": true, "namedChunks": false}, "staging": {"optimization": true, "sourceMap": true, "namedChunks": false}, "dev": {}, "local": {"progress": true, "outputHashing": "none"}}, "defaultConfiguration": "prod", "outputs": ["{options.outputPath.base}"]}, "serve": {"executor": "@nx/angular:dev-server", "dependsOn": ["styles:build", "icon-font:build"], "options": {"buildTarget": "leasing:build:local", "port": 4217}}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectName}"], "options": {"jestConfig": "apps/leasing/jest.config.ts"}}, "lint": {"executor": "@nx/eslint:lint", "options": {"lintFilePatterns": ["apps/leasing/src", "{projectRoot}/src/**/*.{ts,html}"]}}, "stylelint": {"executor": "nx-stylelint:lint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["apps/leasing/src/app/**/*.css", "apps/leasing/src/app/**/*.scss"]}}}}