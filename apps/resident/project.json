{"name": "resident", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/resident/src", "projectType": "application", "prefix": "r", "tags": ["type:website"], "implicitDependencies": ["styles", "icon-font"], "targets": {"build": {"executor": "@nx/angular:application", "options": {"baseHref": "/", "outputPath": {"base": "dist/apps/resident", "browser": ""}, "index": "apps/resident/src/index.html", "polyfills": ["zone.js", "apps/resident/src/global-polyfill.ts"], "tsConfig": "apps/resident/tsconfig.app.json", "crossOrigin": "use-credentials", "outputHashing": "all", "preserveSymlinks": true, "progress": false, "allowedCommonJsDependencies": ["@aws-crypto/sha256-js", "@myndmanagement/styles", "date-fns", "js-cookie", "isomorphic-unfetch", "lodash", "qs", "resize-sensor", "url", "uuid", "smartystreets-javascript-sdk", "deepmerge", "j<PERSON><PERSON>", "save-as", "lodash.isequal", "pusher-js", "rosie", "cookie", "downloadjs", "buffer", "file-saver", "<PERSON><PERSON><PERSON><PERSON>", "quill-delta"], "budgets": [{"type": "anyComponentStyle", "maximumWarning": "6kb"}], "stylePreprocessorOptions": {"sass": {"silenceDeprecations": ["mixed-decls", "color-functions", "global-builtin", "import"]}, "includePaths": ["apps/resident/src"]}, "assets": [{"glob": "**/*", "input": "apps/resident/src/assets", "output": "assets"}, {"glob": "*.js", "input": "apps/resident/src/environments", "output": "assets"}, {"glob": "**/*", "ignore": ["inline-locale-files/**", "additional-locale/**"], "input": "node_modules/ngx-extended-pdf-viewer/assets/", "output": "assets"}], "styles": ["apps/resident/src/styles.scss", "node_modules/normalize.css/normalize.css", "dist/packages/icon-font/font.css", "node_modules/ngx-toastr/toastr.css", "packages/styles/styles/toastr.scss", "packages/styles/styles/fix-quill-bullet-list-styles.scss", "node_modules/quill/dist/quill.snow.css", "node_modules/@angular/cdk/overlay-prebuilt.css", "node_modules/@ionic/angular/css/core.css", "node_modules/@ionic/angular/css/display.css", "node_modules/@ionic/angular/css/float-elements.css"], "extractLicenses": false, "sourceMap": true, "optimization": false, "namedChunks": true, "plugins": ["apps/resident/ci-build-number.plugin.js"], "browser": "apps/resident/src/main.ts"}, "configurations": {"prod": {"assets": ["apps/resident/src/assets", {"glob": "*.tpl", "input": "apps/resident/src/environments", "output": "assets"}, {"glob": "*.js", "input": "apps/resident/src/environments/prod", "output": "assets"}, {"glob": "**/*", "input": "apps/resident/src/public/prod/", "output": "/"}, {"glob": "**/*", "ignore": ["inline-locale-files/**", "additional-locale/**"], "input": "node_modules/ngx-extended-pdf-viewer/assets/", "output": "assets"}], "extractLicenses": true, "optimization": true, "sourceMap": true, "namedChunks": false}, "staging": {"assets": ["apps/resident/src/assets", {"glob": "*.tpl", "input": "apps/resident/src/environments", "output": "assets"}, {"glob": "*.js", "input": "apps/resident/src/environments/staging", "output": "assets"}, {"glob": "**/*", "input": "apps/resident/src/public/dev/", "output": "/"}, {"glob": "**/*", "ignore": ["inline-locale-files/**", "additional-locale/**"], "input": "node_modules/ngx-extended-pdf-viewer/assets/", "output": "assets"}], "optimization": false, "sourceMap": true, "namedChunks": true}, "dev": {"assets": ["apps/resident/src/assets", {"glob": "*.tpl", "input": "apps/resident/src/environments", "output": "assets"}, {"glob": "*.js", "input": "apps/resident/src/environments/dev", "output": "assets"}, {"glob": "**/*", "input": "apps/resident/src/public/dev/", "output": "/"}, {"glob": "**/*", "ignore": ["inline-locale-files/**", "additional-locale/**"], "input": "node_modules/ngx-extended-pdf-viewer/assets/", "output": "assets"}]}, "local": {"progress": true, "outputHashing": "none"}}, "defaultConfiguration": "prod", "outputs": ["{options.outputPath.base}"]}, "serve": {"executor": "@nx/angular:dev-server", "dependsOn": ["styles:build", "icon-font:build"], "options": {"buildTarget": "resident:build:local", "host": "0.0.0.0"}}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectName}"], "options": {"jestConfig": "apps/resident/jest.config.ts"}}, "lint": {"executor": "@nx/eslint:lint", "options": {"lintFilePatterns": ["{projectRoot}/src/**/*.{ts,html}"]}}, "stylelint": {"executor": "nx-stylelint:lint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["apps/resident/src/**/*.css", "apps/resident/src/**/*.scss"]}}, "build-mobile-config": {"executor": "nx:run-commands", "defaultConfiguration": "staging", "configurations": {"prod": {}, "staging": {}}, "options": {"cwd": "{projectRoot}", "parallel": false, "forwardAllArgs": false, "commands": ["IONIC_UPDATES_METHOD=$([ '{args.disable-ionic-updates}' = 'true' ] && echo 'none' || echo 'background') envsubst < ./capacitor.config.json.tpl >./capacitor.config.json"]}}, "cap-build": {"executor": "nx:run-commands", "defaultConfiguration": "staging", "configurations": {"prod": {}, "staging": {}}, "options": {"cwd": "{projectRoot}", "parallel": false, "forwardAllArgs": true, "commands": ["npx cap build {args.platform}"]}}, "cap-run": {"executor": "nx:run-commands", "defaultConfiguration": "staging", "configurations": {"prod": {}, "staging": {}}, "options": {"cwd": "{projectRoot}", "parallel": false, "forwardAllArgs": true, "commands": ["npx cap run {args.platform}"]}}, "cap-sync": {"executor": "nx:run-commands", "defaultConfiguration": "staging", "configurations": {"prod": {}, "staging": {}}, "options": {"cwd": "{projectRoot}", "parallel": false, "forwardAllArgs": true, "commands": ["npx cap sync {args.platform}"]}}, "fastlane-build": {"executor": "nx:run-commands", "dependsOn": ["build"], "defaultConfiguration": "staging", "configurations": {"prod": {}, "staging": {}}, "options": {"cwd": "{projectRoot}", "parallel": false, "forwardAllArgs": true, "commands": ["bundle exec fastlane {args.platform} build --verbose"]}}, "appflow-build": {"executor": "nx:run-commands", "defaultConfiguration": "staging", "configurations": {"prod": {}, "staging": {}}, "options": {"parallel": false, "forwardAllArgs": true, "commands": ["appflow build web --json --app-id=$IONIC_APP_ID --environment=$IONIC_UPDATES_CHANNEL"]}}, "appflow-deploy": {"executor": "nx:run-commands", "defaultConfiguration": "staging", "configurations": {"prod": {}, "staging": {}}, "options": {"parallel": false, "forwardAllArgs": true, "commands": ["appflow deploy web --app-id=$IONIC_APP_ID --destination=$IONIC_UPDATES_CHANNEL", "appflow live-update set-native-versions --app-id=$IONIC_APP_ID --ios-min=$APP_VERSION --android-min=$APP_VERSION"]}}}}