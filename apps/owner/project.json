{"name": "owner", "$schema": "../../node_modules/nx/schemas/project-schema.json", "projectType": "application", "sourceRoot": "apps/owner/src", "prefix": "o", "tags": ["type:website"], "implicitDependencies": ["styles", "icon-font"], "targets": {"build": {"executor": "@nx/angular:application", "outputs": ["{options.outputPath.base}"], "options": {"baseHref": "/", "outputPath": {"base": "dist/apps/owner", "browser": ""}, "index": "apps/owner/src/index.html", "polyfills": ["apps/owner/src/polyfills.ts"], "tsConfig": "apps/owner/tsconfig.app.json", "crossOrigin": "use-credentials", "outputHashing": "all", "preserveSymlinks": true, "progress": false, "allowedCommonJsDependencies": ["@aws-crypto/sha256-browser", "@aws-crypto/sha256-js", "@myndmanagement/styles", "angular2-text-mask", "aws-sdk/global", "buffer", "chart.js", "chartjs-plugin-datalabels", "cookie", "crypto-js", "date-fns", "downloadjs", "fast-deep-equal", "file-saver", "is-callable", "isomorphic-unfetch", "js-cookie", "j<PERSON><PERSON>", "lodash", "lodash.clonedeep", "lodash.isequal", "mobile-detect", "<PERSON><PERSON><PERSON><PERSON>", "pusher-js", "qs", "resize-sensor", "rosie", "safe-area-insets", "save-as", "smartystreets-javascript-sdk", "url", "uuid", "plyr", "quill-delta", "bowser", "zen-observable", "<PERSON><PERSON><PERSON>", "mobile-device-detect"], "stylePreprocessorOptions": {"sass": {"silenceDeprecations": ["mixed-decls", "color-functions", "global-builtin", "import"]}, "includePaths": ["apps/owner/src"]}, "assets": [{"glob": "**/*", "input": "apps/owner/src/assets", "output": "assets"}, {"glob": "*.js", "input": "apps/owner/src/environments", "output": "assets"}, {"glob": "*", "input": "packages/styles/assets/websites", "output": "."}, {"glob": "**/*", "ignore": ["inline-locale-files/**", "additional-locale/**"], "input": "node_modules/ngx-extended-pdf-viewer/assets/", "output": "assets"}], "styles": ["apps/owner/src/styles.scss", "dist/packages/icon-font/font.css", "node_modules/ngx-toastr/toastr.css", "node_modules/quill/dist/quill.snow.css", "node_modules/@angular/cdk/overlay-prebuilt.css", "node_modules/plyr/src/sass/plyr.scss", "node_modules/@fontsource/inter/index.css"], "scripts": ["apps/owner/src/scripts/chartjs-rounded-bar-charts.js"], "budgets": [{"type": "anyComponentStyle", "maximumWarning": "14kb"}], "extractLicenses": false, "sourceMap": true, "optimization": false, "namedChunks": true, "plugins": ["apps/owner/ci-build-number.plugin.js"], "browser": "apps/owner/src/main.ts"}, "configurations": {"prod": {"extractLicenses": true, "optimization": true, "sourceMap": true, "namedChunks": false, "assets": [{"glob": "**/*", "input": "apps/owner/src/assets", "output": "assets"}, {"glob": "*.tpl", "input": "apps/owner/src/environments", "output": "assets"}, {"glob": "*.js", "input": "apps/owner/src/environments/prod", "output": "assets"}, {"glob": "*", "input": "packages/styles/assets/websites", "output": "."}, {"glob": "**/*", "ignore": ["inline-locale-files/**", "additional-locale/**"], "input": "node_modules/ngx-extended-pdf-viewer/assets/", "output": "assets"}, {"glob": "**/*", "input": "apps/owner/src/public/prod/", "output": "/"}]}, "staging": {"optimization": true, "sourceMap": true, "namedChunks": false, "assets": [{"glob": "**/*", "input": "apps/owner/src/assets", "output": "assets"}, {"glob": "*.tpl", "input": "apps/owner/src/environments", "output": "assets"}, {"glob": "*.js", "input": "apps/owner/src/environments/staging", "output": "assets"}, {"glob": "*", "input": "packages/styles/assets/websites", "output": "."}, {"glob": "**/*", "ignore": ["inline-locale-files/**", "additional-locale/**"], "input": "node_modules/ngx-extended-pdf-viewer/assets/", "output": "assets"}, {"glob": "**/*", "input": "apps/owner/src/public/dev/", "output": "/"}]}, "dev": {"assets": [{"glob": "**/*", "input": "apps/owner/src/assets", "output": "assets"}, {"glob": "*.tpl", "input": "apps/owner/src/environments", "output": "assets"}, {"glob": "*.js", "input": "apps/owner/src/environments/dev", "output": "assets"}, {"glob": "*", "input": "packages/styles/assets/websites", "output": "."}, {"glob": "**/*", "ignore": ["inline-locale-files/**", "additional-locale/**"], "input": "node_modules/ngx-extended-pdf-viewer/assets/", "output": "assets"}, {"glob": "**/*", "input": "apps/owner/src/public/dev/", "output": "/"}]}, "local": {"progress": true, "outputHashing": "none"}}, "defaultConfiguration": "prod"}, "serve": {"executor": "@nx/angular:dev-server", "options": {"buildTarget": "owner:build:local"}}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectName}"], "options": {"jestConfig": "apps/owner/jest.config.ts", "passWithNoTests": false}}, "lint": {"executor": "@nx/eslint:lint", "options": {"lintFilePatterns": ["apps/owner/src", "{projectRoot}/src/**/*.{ts,html}"]}}, "stylelint": {"executor": "nx-stylelint:lint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["apps/owner/src/app/**/*.css", "apps/owner/src/app/**/*.scss", "apps/owner/src/styles/**/*.scss"]}}, "build-mobile-config": {"executor": "nx:run-commands", "defaultConfiguration": "staging", "configurations": {"prod": {}, "staging": {}}, "options": {"cwd": "{projectRoot}", "parallel": false, "forwardAllArgs": false, "commands": ["IONIC_UPDATES_METHOD=$([ '{args.disable-ionic-updates}' = 'true' ] && echo 'none' || echo 'background') envsubst < ./capacitor.config.json.tpl >./capacitor.config.json"]}}, "cap-build": {"executor": "nx:run-commands", "defaultConfiguration": "staging", "configurations": {"prod": {}, "staging": {}}, "options": {"cwd": "{projectRoot}", "parallel": false, "forwardAllArgs": true, "commands": ["npx cap build {args.platform}"]}}, "cap-run": {"executor": "nx:run-commands", "defaultConfiguration": "staging", "configurations": {"prod": {}, "staging": {}}, "options": {"cwd": "{projectRoot}", "parallel": false, "forwardAllArgs": true, "commands": ["npx cap run {args.platform}"]}}, "cap-sync": {"executor": "nx:run-commands", "defaultConfiguration": "staging", "configurations": {"prod": {}, "staging": {}}, "options": {"cwd": "{projectRoot}", "parallel": false, "forwardAllArgs": true, "commands": ["npx cap sync {args.platform}"]}}, "fastlane-build": {"executor": "nx:run-commands", "dependsOn": ["build"], "defaultConfiguration": "staging", "configurations": {"prod": {}, "staging": {}}, "options": {"cwd": "{projectRoot}", "parallel": false, "forwardAllArgs": true, "commands": ["bundle exec fastlane {args.platform} build --verbose"]}}, "appflow-build": {"executor": "nx:run-commands", "defaultConfiguration": "staging", "configurations": {"prod": {}, "staging": {}}, "options": {"parallel": false, "forwardAllArgs": true, "commands": ["appflow build web --json --app-id=$IONIC_APP_ID --environment=$IONIC_UPDATES_CHANNEL"]}}, "appflow-deploy": {"executor": "nx:run-commands", "defaultConfiguration": "staging", "configurations": {"prod": {}, "staging": {}}, "options": {"parallel": false, "forwardAllArgs": true, "commands": ["appflow deploy web --app-id=$IONIC_APP_ID --destination=$IONIC_UPDATES_CHANNEL", "appflow live-update set-native-versions --app-id=$IONIC_APP_ID --ios-min=$APP_VERSION --android-min=$APP_VERSION"]}}}}