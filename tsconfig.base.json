{"compileOnSave": false, "compilerOptions": {"baseUrl": ".", "allowSyntheticDefaultImports": true, "declaration": false, "downlevelIteration": true, "emitDecoratorMetadata": true, "esModuleInterop": true, "experimentalDecorators": true, "forceConsistentCasingInFileNames": true, "importHelpers": true, "noEmitOnError": true, "noFallthroughCasesInSwitch": true, "noImplicitReturns": false, "noImplicitThis": true, "resolveJsonModule": true, "skipDefaultLibCheck": true, "skipLibCheck": true, "sourceMap": true, "strict": false, "strictBindCallApply": true, "strictFunctionTypes": true, "stripInternal": true, "useDefineForClassFields": false, "strictPropertyInitialization": false, "moduleResolution": "node", "module": "esnext", "target": "es2022", "lib": ["es2022", "dom"], "types": ["node", "jest", "<PERSON>i", "gapi.auth2"], "paths": {"@myndmanagement/accounting-audit": ["packages/accounting/audit/src/index.ts"], "@myndmanagement/accounting-charge": ["packages/accounting/charge/src/index.ts"], "@myndmanagement/accounting-core": ["packages/accounting/core/src/index.ts"], "@myndmanagement/accounting-receipt": ["packages/accounting/receipt/src/index.ts"], "@myndmanagement/accounting-recurring-invoice": ["packages/accounting/recurring-invoice/src/index.ts"], "@myndmanagement/accounting-transfer": ["packages/accounting/transfer/src/index.ts"], "@myndmanagement/actions": ["packages/actions/src/index.ts"], "@myndmanagement/address": ["packages/address/src/index.ts"], "@myndmanagement/analytics": ["packages/analytics/src/index.ts"], "@myndmanagement/angular-resource-utils": ["packages/angular-utils/resource/src/index.ts"], "@myndmanagement/angular-signal-utils": ["packages/angular-utils/signal/src/index.ts"], "@myndmanagement/api-access-items": ["packages/api/access-items/src/index.ts"], "@myndmanagement/api-actions": ["packages/api/actions/src/index.ts"], "@myndmanagement/api-activity-feed": ["packages/api/activity-feed/src/index.ts"], "@myndmanagement/api-attachments": ["packages/api/attachments/src/index.ts"], "@myndmanagement/api-bank-accounts": ["packages/api/bank-accounts/src/index.ts"], "@myndmanagement/api-bankruptcy": ["packages/api/bankruptcy/src/index.ts"], "@myndmanagement/api-bo-users": ["packages/api/bo-users/src/index.ts"], "@myndmanagement/api-cases": ["packages/api/cases/src/index.ts"], "@myndmanagement/api-charge-codes": ["packages/api/charge-codes/src/index.ts"], "@myndmanagement/api-communications": ["packages/api/communications/src/index.ts"], "@myndmanagement/api-communities": ["packages/api/communities/src/index.ts"], "@myndmanagement/api-concession-codes": ["packages/api/concession-codes/src/index.ts"], "@myndmanagement/api-csat": ["packages/api/csat/src/index.ts"], "@myndmanagement/api-entities": ["packages/api/entities/src/index.ts"], "@myndmanagement/api-entity-distributions": ["packages/api/entity-distributions/src/index.ts"], "@myndmanagement/api-files": ["packages/api/files/src/index.ts"], "@myndmanagement/api-geography": ["packages/api/geography/src/index.ts"], "@myndmanagement/api-gl-accounts": ["packages/api/gl-accounts/src/index.ts"], "@myndmanagement/api-google-calendar": ["packages/api/google-calendar/src/index.ts"], "@myndmanagement/api-google-spreadsheet": ["packages/api/google-spreadsheet/src/index.ts"], "@myndmanagement/api-hoa": ["packages/api/hoa/src/index.ts"], "@myndmanagement/api-in-house-tech": ["packages/api/in-house-tech/src/index.ts"], "@myndmanagement/api-inspections": ["packages/api/inspections/src/index.ts"], "@myndmanagement/api-institutional-partners": ["packages/api/institutional-partners/src/index.ts"], "@myndmanagement/api-lease-accounting": ["packages/api/lease-accounting/src/index.ts"], "@myndmanagement/api-leases": ["packages/api/leases/src/index.ts"], "@myndmanagement/api-line-items": ["packages/api/line-items/src/index.ts"], "@myndmanagement/api-mynders-locations": ["packages/api/mynders-locations/src/index.ts"], "@myndmanagement/api-owner": ["packages/api/owner/src/index.ts"], "@myndmanagement/api-owner-contributions": ["packages/api/owner-contributions/src/index.ts"], "@myndmanagement/api-persons": ["packages/api/persons/src/index.ts"], "@myndmanagement/api-price-book-entry": ["packages/api/price-book-entry/src/index.ts"], "@myndmanagement/api-properties": ["packages/api/properties/src/index.ts"], "@myndmanagement/api-prospects": ["packages/api/prospects/src/index.ts"], "@myndmanagement/api-recurring-transactions": ["packages/api/recurring-transactions/src/index.ts"], "@myndmanagement/api-service-request-stores": ["packages/api/service-request-stores/src/index.ts"], "@myndmanagement/api-service-requests": ["packages/api/service-requests/src/index.ts"], "@myndmanagement/api-soda": ["packages/api/soda/src/index.ts"], "@myndmanagement/api-subtasks": ["packages/api/subtasks/src/index.ts"], "@myndmanagement/api-tags": ["packages/api/tags/src/index.ts"], "@myndmanagement/api-tasks": ["packages/api/tasks/src/index.ts"], "@myndmanagement/api-taxonomy-rules": ["packages/api/taxonomy-rules/src/index.ts"], "@myndmanagement/api-vendors": ["packages/api/vendors/src/index.ts"], "@myndmanagement/api-work-orders": ["packages/api/work-orders/src/index.ts"], "@myndmanagement/attachments": ["packages/attachments/src/index.ts"], "@myndmanagement/autocomplete-address": ["packages/autocomplete-address/src/index.ts"], "@myndmanagement/back-office-layout": ["packages/back-office-layout/src/index.ts"], "@myndmanagement/badge": ["packages/badge/src/index.ts"], "@myndmanagement/chats": ["packages/chats/src/index.ts"], "@myndmanagement/ci": ["plugins/ci/src/index.ts"], "@myndmanagement/common": ["packages/common/src/index.ts"], "@myndmanagement/common-otto": ["packages/common-otto/src/index.ts"], "@myndmanagement/common-utils": ["packages/common-utils/src/index.ts"], "@myndmanagement/common/testing": ["packages/common/testing/index.ts"], "@myndmanagement/communication": ["packages/communication/src/index.ts"], "@myndmanagement/communication/cases": ["packages/communication/cases/index.ts"], "@myndmanagement/communication/generative-ai": ["packages/communication/generative-ai/index.ts"], "@myndmanagement/devkit": ["plugins/devkit/src/index.ts"], "@myndmanagement/dyn-forms": ["packages/dyn-forms/src/index.ts"], "@myndmanagement/dyn-forms/editable-grid": ["packages/dyn-forms/editable-grid/index.ts"], "@myndmanagement/dynamic-forms": ["packages/dynamic-forms/src/index.ts"], "@myndmanagement/environment": ["packages/environment/src/index.ts"], "@myndmanagement/external-chats": ["packages/external-chats/src/index.ts"], "@myndmanagement/file-manager": ["packages/file-manager/index.ts"], "@myndmanagement/file-manager/core": ["packages/file-manager/core/index.ts"], "@myndmanagement/file-manager/downloader": ["packages/file-manager/downloader/index.ts"], "@myndmanagement/file-manager/editor": ["packages/file-manager/editor/index.ts"], "@myndmanagement/file-manager/uploader": ["packages/file-manager/uploader/index.ts"], "@myndmanagement/file-manager/viewer": ["packages/file-manager/viewer/index.ts"], "@myndmanagement/filterable-view": ["packages/filterable-view/src/index.ts"], "@myndmanagement/forms-otto": ["packages/forms-otto/src/index.ts"], "@myndmanagement/forms-utils": ["packages/forms-utils/src/index.ts"], "@myndmanagement/forms-v2/button": ["packages/forms-v2/button/index.ts"], "@myndmanagement/forms-v2/checkbox": ["packages/forms-v2/checkbox/index.ts"], "@myndmanagement/forms-v2/core": ["packages/forms-v2/core/index.ts"], "@myndmanagement/forms-v2/input": ["packages/forms-v2/input/index.ts"], "@myndmanagement/forms-v2/radio": ["packages/forms-v2/radio/index.ts"], "@myndmanagement/forms-v2/select": ["packages/forms-v2/select/index.ts"], "@myndmanagement/gitmc": ["plugins/gitmc/src/index.ts"], "@myndmanagement/google-maps": ["packages/google-maps/src/index.ts"], "@myndmanagement/history": ["packages/history/src/index.ts"], "@myndmanagement/history-adapter-market-analysis": ["packages/history-adapters/market-analysis/src/index.ts"], "@myndmanagement/history-adapter-unit": ["packages/history-adapters/unit/src/index.ts"], "@myndmanagement/hoa-store": ["packages/hoa/store/src/index.ts"], "@myndmanagement/hoa-ui": ["packages/hoa/ui/src/index.ts"], "@myndmanagement/hoa-utils": ["packages/hoa/utils/src/index.ts"], "@myndmanagement/http": ["packages/http/src/index.ts"], "@myndmanagement/http/testing": ["packages/http/testing/index.ts"], "@myndmanagement/icon-font": ["packages/icon-font/src/index.ts"], "@myndmanagement/inspections": ["packages/inspections/src/index.ts"], "@myndmanagement/kanban-view": ["packages/kanban-view/src/index.ts"], "@myndmanagement/launch-darkly": ["packages/launch-darkly/src/index.ts"], "@myndmanagement/layout": ["packages/layout/src/index.ts"], "@myndmanagement/layout-otto": ["packages/layout-otto/src/index.ts"], "@myndmanagement/layout-otto/sticky": ["packages/layout-otto/sticky/index.ts"], "@myndmanagement/layout/services": ["packages/layout/services/index.ts"], "@myndmanagement/layout/testing": ["packages/layout/testing/index.ts"], "@myndmanagement/leases": ["packages/leases/src/index.ts"], "@myndmanagement/leases/testing": ["packages/leases/testing/index.ts"], "@myndmanagement/linter": ["plugins/linter/src/index.ts"], "@myndmanagement/local-storage": ["packages/local-storage/src/index.ts"], "@myndmanagement/mobile": ["packages/mobile/src/index.ts"], "@myndmanagement/modals": ["packages/modals/src/index.ts"], "@myndmanagement/modals/testing": ["packages/modals/testing/index.ts"], "@myndmanagement/notes": ["packages/notes/src/index.ts"], "@myndmanagement/owner-balance": ["packages/owner-balance/src/index.ts"], "@myndmanagement/owner-communications": ["packages/owner-communications/src/index.ts"], "@myndmanagement/owner-newsletter": ["packages/owner-newsletter/src/index.ts"], "@myndmanagement/permissions": ["packages/permissions/src/index.ts"], "@myndmanagement/properties": ["packages/properties/src/index.ts"], "@myndmanagement/prospects": ["packages/prospects/src/index.ts"], "@myndmanagement/pusher": ["packages/pusher/src/index.ts"], "@myndmanagement/pusher/testing": ["packages/pusher/testing/index.ts"], "@myndmanagement/recently-viewed": ["packages/recently-viewed/src/index.ts"], "@myndmanagement/related-actions": ["packages/related-actions/src/index.ts"], "@myndmanagement/related-cases": ["packages/related-cases/src/index.ts"], "@myndmanagement/reporting": ["packages/reporting/src/index.ts"], "@myndmanagement/reporting/catalogue-data": ["packages/reporting/catalogue-data/index.ts"], "@myndmanagement/reporting/core": ["packages/reporting/core/index.ts"], "@myndmanagement/reporting/sigma": ["packages/reporting/sigma/index.ts"], "@myndmanagement/sentiment": ["packages/sentiment/src/index.ts"], "@myndmanagement/service-requests-categories": ["packages/service-requests/categories/src/index.ts"], "@myndmanagement/service-requests-shared": ["packages/service-requests/shared/src/index.ts"], "@myndmanagement/service-requests-sr-form": ["packages/service-requests/service-request-form/src/index.ts"], "@myndmanagement/service-requests-shared-dashboard": ["packages/service-requests/shared-dashboard/src/index.ts"], "@myndmanagement/api-work-orders-sla": ["packages/api/work-orders-sla/src/index.ts"], "@myndmanagement/service-requests-shared-line-items": ["packages/service-requests/shared-line-items/src/index.ts"], "@myndmanagement/service-worker": ["packages/service-worker/src/index.ts"], "@myndmanagement/service-worker/sw-config": ["packages/service-worker/sw-config/index.ts"], "@myndmanagement/service-worker/sw-service": ["packages/service-worker/sw-service/index.ts"], "@myndmanagement/store-select": ["packages/store-select/src/index.ts"], "@myndmanagement/styles": ["packages/styles/src/index.ts"], "@myndmanagement/subtasks-core": ["packages/subtasks/core/src/index.ts"], "@myndmanagement/subtasks-escalations": ["packages/subtasks/escalations/src/index.ts"], "@myndmanagement/subtasks-ui": ["packages/subtasks/ui/src/index.ts"], "@myndmanagement/subtasks-workflow-integration": ["packages/subtasks/workflow-integration/src/index.ts"], "@myndmanagement/test": ["packages/test/src/index.ts"], "@myndmanagement/ui-avatar": ["packages/ui/avatar/src/index.ts"], "@myndmanagement/ui-card": ["packages/ui/card/src/index.ts"], "@myndmanagement/ui-configured-table": ["packages/ui/configured-table/src/index.ts"], "@myndmanagement/ui-forms": ["packages/ui/forms/src/index.ts"], "@myndmanagement/ui-modals": ["packages/ui/modals/src/index.ts"], "@myndmanagement/ui-modals/testing": ["packages/ui/modals/testing/index.ts"], "@myndmanagement/ui-search": ["packages/ui-search/src/index.ts"], "@myndmanagement/ui-table": ["packages/ui/table/src/index.ts"], "@myndmanagement/ui-toast": ["packages/ui/toast/src/index.ts"], "@myndmanagement/ui-toast/testing": ["packages/ui/toast/testing/index.ts"], "@myndmanagement/ui-tooltip": ["packages/ui/tooltip/src/index.ts"], "@myndmanagement/user-card": ["packages/user-card/src/index.ts"], "@myndmanagement/users": ["packages/users/src/index.ts"], "@myndmanagement/vendors": ["packages/vendors/src/index.ts"], "@myndmanagement/work-order-invoices": ["packages/work-order-invoices/src/index.ts"], "@myndmanagement/work-order-line-items": ["packages/work-order-line-items/src/index.ts"], "@myndmanagement/workflow-timeline": ["packages/workflow-timeline/src/index.ts"], "@ng-doc/generated": ["./ng-doc/docs/index.ts"], "@ng-doc/generated/*": ["./ng-doc/docs/*"]}}, "angularCompilerOptions": {"enableI18nLegacyMessageIdFormat": false, "strictDomEventTypes": false, "strictInjectionParameters": true, "strictInputAccessModifiers": true, "strictMetadataEmit": true, "strictInputTypes": false, "strictOutputEventTypes": false, "strictTemplates": true}, "exclude": ["node_modules", "tmp"]}