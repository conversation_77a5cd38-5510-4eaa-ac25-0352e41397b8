name: Pull Request

on:
  pull_request:
    types:
      - opened
      - synchronize
      - reopened
      - ready_for_review

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

env:
  SANDBOX_ID: ${{ github.event.number }}
  NX_SELF_HOSTED_REMOTE_CACHE_SERVER: http://nx-cache.roofstock.ws/repo/${{ github.event.repository.name }}
  NX_SELF_HOSTED_REMOTE_CACHE_ACCESS_TOKEN: ${{ secrets.NX_CACHE_ACCESS_TOKEN }}
  NODE_TLS_REJECT_UNAUTHORIZED: 0

jobs:
  commitlint:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: wagoid/commitlint-github-action@v6
        with:
          configFile: '.commitlintrc.json'

  update-assignee:
    runs-on: ubuntu-latest
    if: ${{ github.event.action == 'opened' && github.event.pull_request.assignee == null }}
    steps:
      - uses: actions/checkout@v4
        with:
          sparse-checkout: |
            .github

      - name: Update Pull Request Assignee
        env:
          GH_TOKEN: ${{ github.token }}
        run: gh pr edit ${{ github.event.number }} --add-assignee ${{ github.actor }}

  prepare:
    runs-on: ubuntu-latest
    outputs:
      affected-projects: ${{ steps.define-affected-projects.outputs.affected-projects }}
      affected-projects-count: ${{ steps.define-affected-projects.outputs.affected-projects-count }}
      affected-applications: ${{ steps.define-affected-projects.outputs.affected-applications }}
      affected-applications-count: ${{ steps.define-affected-projects.outputs.affected-applications-count }}
      affected-libraries: ${{ steps.define-affected-projects.outputs.affected-libraries }}
      affected-libraries-count: ${{ steps.define-affected-projects.outputs.affected-libraries-count }}
      deploy-configs: ${{ steps.define-affected-projects.outputs.deploy-configs }}
      back-office-deploy-configs: ${{ steps.define-affected-projects.outputs.back-office-deploy-configs }}
      non-back-office-deploy-configs: ${{ steps.define-affected-projects.outputs.non-back-office-deploy-configs }}
      e2e-tags: ${{ steps.define-affected-projects.outputs.e2e-tags }}
      sandbox-id: ${{ env.SANDBOX_ID }}
      sandbox-prefix: ${{ format('sbx/{0}', env.SANDBOX_ID) }}
      unit-tests-matrix: ${{ steps.unit-tests-matrix.outputs.result }}

    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 120
          fetch-tags: false

      - name: Dump GitHub context
        env:
          GITHUB_CONTEXT: ${{ toJson(github) }}
        run: echo "$GITHUB_CONTEXT"

      - name: Setup node and install dependencies
        uses: ./.github/actions/setup-node

      - name: Define Affected Projects
        id: define-affected-projects
        uses: ./.github/actions/nx-affected
        with:
          nx-base: ${{ github.event.pull_request.base.sha }}
          nx-head: ${{ github.event.pull_request.head.sha }}

      - name: Generate unit tests
        id: unit-tests-matrix
        uses: ./.github/actions/get-unit-tests-matrix
        with:
          libs: ${{ steps.define-affected-projects.outputs.affected-libraries }}
          apps: ${{ steps.define-affected-projects.outputs.affected-applications }}

      - name: Generate job summary
        run: |
          {
            echo "### :rocket: Workflow outputs"
            echo "| Arg                | Value |"
            echo "| ------------------ | ----- |"
            echo "| Affected projects (${{ steps.define-affected-projects.outputs.affected-projects-count }}) | ${{ join(fromJson(steps.define-affected-projects.outputs.affected-projects), ', ') }} |"
            echo "| Affected libraries (${{ steps.define-affected-projects.outputs.affected-libraries-count }}) | ${{ join(fromJson(steps.define-affected-projects.outputs.affected-libraries), ', ') }} |"
            echo "| Affected applications (${{ steps.define-affected-projects.outputs.affected-applications-count }}) | ${{ join(fromJson(steps.define-affected-projects.outputs.affected-applications), ', ') }} |"
            echo "| E2E tags | ${{ join(fromJson(steps.define-affected-projects.outputs.e2e-tags), ', ') }} |"
          } >> $GITHUB_STEP_SUMMARY

  lint:
    needs: prepare
    if: needs.prepare.outputs.affected-projects-count != 0
    runs-on: otto-fargate-common-large-runners
    timeout-minutes: 30
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 1

      - name: Setup node and install dependencies
        uses: ./.github/actions/setup-node

      - name: Lint
        run: npx nx run-many -t lint --parallel=15 --verbose -p ${{ join(fromJson(needs.prepare.outputs.affected-projects), ',') }}

  build:
    needs: prepare
    if: needs.prepare.outputs.affected-projects-count != 0
    runs-on: otto-fargate-common-large-runners
    timeout-minutes: 30
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 1

      - name: Setup node and install dependencies
        uses: ./.github/actions/setup-node

      - name: Build affected projects
        run: npx nx run-many -t build --parallel=5 -p ${{ join(fromJson(needs.prepare.outputs.affected-projects), ',') }}

      # Required for sandbox creation. Utilizes Nx cache to ensure build performance remains optimal.
      # For more information on Nx caching, visit: https://nx.dev/concepts/how-caching-works
      - name: Build websites
        if: fromJSON(needs.prepare.outputs.deploy-configs)[0] != null
        run: npx nx run-many -t build --parallel=5 --projects=tag:type:website

  unit-tests:
    needs: prepare
    if: needs.prepare.outputs.affected-projects-count != 0
    secrets: inherit
    uses: ./.github/workflows/unit-tests.yml
    with:
      matrix: ${{ needs.prepare.outputs.unit-tests-matrix }}

  test:
    needs: [prepare, unit-tests]
    if: ${{ always() && needs.prepare.outputs.affected-projects-count != 0 }}
    runs-on: ubuntu-latest
    steps:
      - name: Exit with failure if workflow failed
        if: ${{ contains(fromJSON('["failure", "cancelled"]'), needs.unit-tests.result) }}
        run: exit 1

  deploy-sandbox:
    needs: [prepare, build]
    if: ${{ fromJSON(needs.prepare.outputs.non-back-office-deploy-configs)[0] != null && fromJSON(needs.prepare.outputs.deploy-configs)[0] != null }}
    uses: ./.github/workflows/deploy-sandbox.yml
    secrets: inherit
    name: Deploy Sandbox ${{ matrix.deploy-config.projectName }}

    strategy:
      fail-fast: false
      matrix:
        deploy-config: ${{ fromJson(needs.prepare.outputs.non-back-office-deploy-configs) }}

    with:
      sandbox-prefix: ${{ needs.prepare.outputs.sandbox-prefix }}
      service-name: ${{ matrix.deploy-config.config.serviceName }}
      project-name: ${{ matrix.deploy-config.projectName }}
      s3-bucket-prefix: ${{ matrix.deploy-config.config.s3-bucket-prefix }}
      base-href: ${{ matrix.deploy-config.config.path-prefix }}
      dns-prefix: ${{ matrix.deploy-config.config.dns-prefix }}
      dist-path: ${{ matrix.deploy-config.distPath }}

  deploy-back-office-sandbox:
    needs: [prepare, build]
    if: ${{ fromJSON(needs.prepare.outputs.back-office-deploy-configs)[0] != null && fromJSON(needs.prepare.outputs.deploy-configs)[0] != null }}
    uses: ./.github/workflows/deploy-back-office-sandbox.yml
    secrets: inherit
    name: Deploy Sandbox BackOffice

    with:
      sandbox-prefix: ${{ needs.prepare.outputs.sandbox-prefix }}
      deploy-configs: ${{ needs.prepare.outputs.back-office-deploy-configs }}

  deployment-report:
    needs: [prepare, deploy-sandbox, deploy-back-office-sandbox]
    if: ${{ !failure() && !cancelled() && fromJSON(needs.prepare.outputs.deploy-configs)[0] != null }}
    runs-on: ubuntu-latest
    steps:
      - name: Generate services list
        id: services
        env:
          BACK_OFFICE_DEPLOY_CONFIG: ${{ needs.prepare.outputs.back-office-deploy-configs }}
          NON_BACK_OFFICE_DEPLOY_CONFIGS: ${{ needs.prepare.outputs.non-back-office-deploy-configs }}
        run: |
          BACK_OFFICE_DEPLOY_CONFIG=$(echo $BACK_OFFICE_DEPLOY_CONFIG | jq -c 'map(select(.config.serviceName == "otto-back-office-host-website"))')
          RESULT=$(jq -s 'add' <(echo "$NON_BACK_OFFICE_DEPLOY_CONFIGS") <(echo "$BACK_OFFICE_DEPLOY_CONFIG"))

          RESULT=$(echo $RESULT | jq -c '
            map({
              serviceName: .config.serviceName,
              dev: ("https://" + .config."dns-prefix" + ".dev.mynd-internal.co/${{ needs.prepare.outputs.sandbox-prefix }}"),
              staging: ("https://" + .config."dns-prefix" + ".staging.mynd-internal.co/${{ needs.prepare.outputs.sandbox-prefix }}")
            })
          ')

          echo "result=$RESULT" >> $GITHUB_OUTPUT

      - uses: buildingcash/json-to-markdown-table-action@v1
        id: table
        with:
          json: ${{ steps.services.outputs.result }}

      - name: Find Comment
        uses: peter-evans/find-comment@v3
        id: fc
        with:
          issue-number: ${{ github.event.pull_request.number }}
          comment-author: 'github-actions[bot]'
          body-includes: Sandbox Deployments

      - name: Create comment
        uses: peter-evans/create-or-update-comment@v4
        with:
          issue-number: ${{ github.event.pull_request.number }}
          comment-id: ${{ steps.fc.outputs.comment-id }}
          edit-mode: replace
          body: |
            ### Sandbox Deployments

            <details>
              <summary>View Deployments</summary>
              <br/>

              ${{ steps.table.outputs.table }}
            </details>

  e2e:
    needs: [prepare, deploy-sandbox, deploy-back-office-sandbox]
    uses: roofstock/ci-cd/.github/workflows/e2e-test-by-tags.yml@main
    if: ${{ !failure() && !cancelled() && needs.prepare.outputs.e2e-tags != '[]' && github.event.pull_request.draft == false }}
    secrets: inherit
    with:
      tags: ${{ needs.prepare.outputs.e2e-tags }}
      sandbox-id: ${{ needs.prepare.outputs.sandbox-id }}
      issue-number: ${{ github.event.pull_request.number }}
      legacy-folder-structure: true
      statistics: false
