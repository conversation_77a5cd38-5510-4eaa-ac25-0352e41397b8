name: Push to main

on:
  push:
    branches:
      - main

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: false

env:
  NX_SELF_HOSTED_REMOTE_CACHE_SERVER: http://nx-cache.roofstock.ws/repo/${{ github.event.repository.name }}
  NX_SELF_HOSTED_REMOTE_CACHE_ACCESS_TOKEN: ${{ secrets.NX_CACHE_ACCESS_TOKEN }}
  NODE_TLS_REJECT_UNAUTHORIZED: 0

jobs:
  prepare:
    runs-on: ubuntu-latest
    outputs:
      affected-projects: ${{ steps.define-affected-projects.outputs.affected-projects }}
      affected-projects-count: ${{ steps.define-affected-projects.outputs.affected-projects-count }}
      affected-applications: ${{ steps.define-affected-projects.outputs.affected-applications }}
      affected-applications-count: ${{ steps.define-affected-projects.outputs.affected-applications-count }}
      affected-libraries: ${{ steps.define-affected-projects.outputs.affected-libraries }}
      affected-libraries-count: ${{ steps.define-affected-projects.outputs.affected-libraries-count }}
      deploy-configs: ${{ steps.define-affected-projects.outputs.deploy-configs }}
      deploy-service-name-list: ${{ steps.define-affected-projects.outputs.deploy-service-name-list }}
      e2e-tags: ${{ steps.define-affected-projects.outputs.e2e-tags }}
      unit-tests-matrix: ${{ steps.unit-tests-matrix.outputs.result }}

    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 120
          fetch-tags: false

      - name: Dump GitHub context
        env:
          GITHUB_CONTEXT: ${{ toJson(github) }}
        run: echo "$GITHUB_CONTEXT"

      - name: Setup node and install dependencies
        uses: ./.github/actions/setup-node

      - name: Calculate Nx base
        id: calculate-nx-base
        env:
          GH_TOKEN: ${{ github.token }}
        run: |
          NX_BASE=$(gh run list -w="${{ github.workflow }}" --status=completed --json=conclusion,headSha --jq='[.[] | select(.conclusion != "cancelled")][0].headSha')
          echo "result=$NX_BASE" >> $GITHUB_OUTPUT

      - name: Define Affected Projects
        id: define-affected-projects
        uses: ./.github/actions/nx-affected
        with:
          nx-base: ${{ steps.calculate-nx-base.outputs.result }}
          nx-head: ${{ github.sha }}

      - name: Generate unit tests
        id: unit-tests-matrix
        uses: ./.github/actions/get-unit-tests-matrix
        with:
          libs: ${{ steps.define-affected-projects.outputs.affected-libraries }}
          apps: ${{ steps.define-affected-projects.outputs.affected-applications }}

      - name: Generate job summary
        run: |
          {
            echo "### :rocket: Workflow outputs"
            echo "| Arg                | Value |"
            echo "| ------------------ | ----- |"
            echo "| Workflow run id    | ${{ github.run_id }} |"
            echo "| Affected projects (${{ steps.define-affected-projects.outputs.affected-projects-count }}) | ${{ join(fromJson(steps.define-affected-projects.outputs.affected-projects), ', ') }} |"
            echo "| Affected libraries (${{ steps.define-affected-projects.outputs.affected-libraries-count }}) | ${{ join(fromJson(steps.define-affected-projects.outputs.affected-libraries), ', ') }} |"
            echo "| Affected applications (${{ steps.define-affected-projects.outputs.affected-applications-count }}) | ${{ join(fromJson(steps.define-affected-projects.outputs.affected-applications), ', ') }} |"
          } >> $GITHUB_STEP_SUMMARY

  lint:
    needs: prepare
    if: needs.prepare.outputs.affected-projects-count != 0
    runs-on: otto-fargate-common-large-runners
    timeout-minutes: 30
    environment: prod
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 1

      - name: Setup node and install dependencies
        uses: ./.github/actions/setup-node

      - name: Lint
        run: npx nx run-many -t lint --parallel=8 -p ${{ join(fromJson(needs.prepare.outputs.affected-projects), ',') }}

  unit-tests:
    needs: prepare
    if: needs.prepare.outputs.affected-projects-count != 0
    secrets: inherit
    uses: ./.github/workflows/unit-tests.yml
    with:
      matrix: ${{ needs.prepare.outputs.unit-tests-matrix }}
      environment: prod

  build:
    needs: prepare
    if: needs.prepare.outputs.affected-projects-count != 0
    runs-on: otto-fargate-common-large-runners
    timeout-minutes: 30
    environment: prod
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 1

      - name: Setup node and install dependencies
        uses: ./.github/actions/setup-node

      - name: Build affected projects
        run: npx nx run-many -t build --parallel=5 -p ${{ join(fromJson(needs.prepare.outputs.affected-projects), ',') }}

      - name: Upload dist artifacts
        uses: actions/upload-artifact@v4
        with:
          name: dist
          path: dist
          include-hidden-files: true

  deploy-website:
    needs: [prepare, build, lint, unit-tests]
    if: fromJSON(needs.prepare.outputs.deploy-configs)[0] != null
    secrets: inherit
    uses: ./.github/workflows/deploy-website.yml
    name: Deploy ${{ matrix.deploy-config.config.serviceName }}

    strategy:
      matrix:
        deploy-config: ${{ fromJSON(needs.prepare.outputs.deploy-configs) }}

    with:
      service-name: ${{ matrix.deploy-config.config.serviceName }}
      project-name: ${{ matrix.deploy-config.projectName }}
      project-root: ${{ matrix.deploy-config.projectRoot }}
      dist-path: ${{ matrix.deploy-config.distPath }}
      base-href: ${{ matrix.deploy-config.config.path-prefix }}

  deploy-docs:
    needs: [prepare, build, lint, unit-tests]
    runs-on: ubuntu-latest
    if: contains(fromJSON(needs.prepare.outputs.affected-projects), 'docs')

    permissions:
      contents: read
      pages: write
      id-token: write

    environment:
      name: docs
      url: ${{ steps.deployment.outputs.page_url }}

    steps:
      - uses: actions/checkout@v4
        with:
          sparse-checkout: |
            .github

      - name: Download dist artifacts
        uses: actions/download-artifact@v4
        with:
          name: dist
          path: dist

      - name: Upload Docs Artifact
        uses: actions/upload-pages-artifact@v3
        with:
          name: otto-docs
          path: 'dist/apps/docs/browser'

      - name: Setup Pages
        uses: actions/configure-pages@v3

      - name: Deploy to GitHub Pages
        id: deployment
        uses: actions/deploy-pages@v4
        with:
          artifact_name: otto-docs

  create-issue:
    runs-on: ubuntu-latest
    needs: [prepare, deploy-website]
    if: fromJSON(needs.prepare.outputs.deploy-configs)[0] != null
    outputs:
      issue-number: ${{ steps.create-issue.outputs.number }}

    steps:
      - uses: actions/checkout@v4
        with:
          sparse-checkout: |
            .github

      - name: Get Pull Request Number
        id: pr-number
        env:
          GH_TOKEN: ${{ github.token }}
        run: |
          PR_NUMBER=$(gh pr list --search ${{ github.sha }} --state merged --json=number --jq='.[0].number')

          echo "number=$PR_NUMBER" >> $GITHUB_OUTPUT

      - name: Create Issue
        uses: dacbd/create-issue-action@main
        id: create-issue
        env:
          REPOSITORY_URL: ${{ github.server_url }}/${{ github.repository }}
          PR_NUMBER: ${{ steps.pr-number.outputs.number }}
          RUN_ID: ${{ github.run_id }}
        with:
          token: ${{ github.token }}
          title: Production Deploy Request for workflow run ${{ env.RUN_ID }}
          assignees: ${{ github.actor }}
          labels: ${{ join(fromJson(needs.prepare.outputs.deploy-service-name-list), ',') }}
          body: |
            ## Production Deploy Request

            - Author: @${{ github.actor }}
            - Commit: ${{ github.sha }}
            - Pull Request: [${{ env.PR_NUMBER }}](${{ env.REPOSITORY_URL }}/pull/${{ env.PR_NUMBER }})
            - Workflow Run: ${{ env.REPOSITORY_URL }}/actions/runs/${{ env.RUN_ID }}

            ### Affected Services
            ${{ join(fromJson(needs.prepare.outputs.deploy-service-name-list), '<br/>') }}

            ### Commands

            Respond to this issue with one of the following commands:
            | Command | Description |
            |--------|--------|
            | `approve` | Trigger production deploy if e2e tests are completed. |
            | `approve \| hotfix` | Trigger production deploy as a hotfix. |

            ### Workflow Run ID
            ${{ env.RUN_ID }}

  e2e:
    needs: [prepare, deploy-website, create-issue]
    uses: roofstock/ci-cd/.github/workflows/e2e-test-by-tags.yml@main
    if: needs.prepare.outputs.e2e-tags != '[]'
    secrets: inherit
    with:
      issue-number: ${{ needs.create-issue.outputs.issue-number }}
      tags: ${{ needs.prepare.outputs.e2e-tags }}
      legacy-folder-structure: true
